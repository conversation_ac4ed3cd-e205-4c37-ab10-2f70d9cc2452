/**
 * Firebase Configuration and Initialization
 * Production-ready Firebase setup for USA EasyNaukri4U
 */

// Firebase configuration - Replace with your actual Firebase project config
const firebaseConfig = {
    apiKey: "AIzaSyBlO042NFLQidZ8Z0Nf4vTZUJk3P-k7aD0",
    authDomain: "usa-easynaukri4u.firebaseapp.com",
    projectId: "usa-easynaukri4u",
    storageBucket: "usa-easynaukri4u.firebasestorage.app",
    messagingSenderId: "1064709303084",
    appId: "1:1064709303084:web:91322bdd3fd21161591a89",
    measurementId: "G-BG39E3ZNP0"
};

// Validate Firebase configuration
if (firebaseConfig.apiKey === "your-api-key-here") {
    console.warn('⚠️ Firebase configuration not set up! Please update js/firebase-config.js with your actual Firebase project configuration.');
} else {
    console.log('✅ Firebase configuration loaded successfully');
}

// Initialize Firebase
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import { getAuth, signInWithEmailAndPassword, signOut, onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, orderBy, where, limit } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Admin email configuration - Only this email can access admin panel
const ADMIN_EMAIL = '<EMAIL>'; // Change this to your admin email

/**
 * Firebase Authentication Manager
 */
class FirebaseAuthManager {
    constructor() {
        this.currentUser = null;
        this.authStateListeners = [];
        this.initAuthStateListener();
    }

    /**
     * Initialize authentication state listener
     */
    initAuthStateListener() {
        onAuthStateChanged(auth, (user) => {
            this.currentUser = user;
            this.notifyAuthStateListeners(user);
        });
    }

    /**
     * Add authentication state listener
     * @param {Function} callback 
     */
    addAuthStateListener(callback) {
        this.authStateListeners.push(callback);
    }

    /**
     * Notify all authentication state listeners
     * @param {Object} user 
     */
    notifyAuthStateListeners(user) {
        this.authStateListeners.forEach(callback => callback(user));
    }

    /**
     * Sign in with email and password
     * @param {string} email 
     * @param {string} password 
     * @returns {Promise}
     */
    async signIn(email, password) {
        try {
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            
            // Check if user is admin
            if (email !== ADMIN_EMAIL) {
                await this.signOut();
                throw new Error('Access denied. Only admin can access this system.');
            }
            
            return userCredential.user;
        } catch (error) {
            console.error('Sign in error:', error);
            throw error;
        }
    }

    /**
     * Sign out current user
     * @returns {Promise}
     */
    async signOut() {
        try {
            await signOut(auth);
        } catch (error) {
            console.error('Sign out error:', error);
            throw error;
        }
    }

    /**
     * Check if current user is authenticated admin
     * @returns {boolean}
     */
    isAdminAuthenticated() {
        return this.currentUser && this.currentUser.email === ADMIN_EMAIL;
    }

    /**
     * Get current user
     * @returns {Object|null}
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * Protect admin routes
     * @param {string} redirectUrl 
     */
    protectAdminRoute(redirectUrl = '/login.html') {
        if (!this.isAdminAuthenticated()) {
            window.location.href = redirectUrl;
            return false;
        }
        return true;
    }
}

/**
 * Firebase Firestore Manager for Jobs
 */
class FirebaseJobManager {
    constructor() {
        this.jobsCollection = 'jobs';
    }

    /**
     * Add a new job to Firestore
     * @param {Object} jobData 
     * @returns {Promise}
     */
    async addJob(jobData) {
        try {
            // Add timestamp and admin info
            const jobWithMetadata = {
                ...jobData,
                createdAt: new Date(),
                updatedAt: new Date(),
                createdBy: auth.currentUser?.email || 'admin',
                status: 'active',
                views: 0,
                applications: 0
            };

            const docRef = await addDoc(collection(db, this.jobsCollection), jobWithMetadata);
            return { id: docRef.id, ...jobWithMetadata };
        } catch (error) {
            console.error('Error adding job:', error);
            throw error;
        }
    }

    /**
     * Get all jobs from Firestore
     * @param {Object} options - Query options
     * @returns {Promise}
     */
    async getJobs(options = {}) {
        try {
            const {
                status = 'active',
                orderByField = 'createdAt',
                orderDirection = 'desc',
                limitCount = null,
                filters = {}
            } = options;

            let q = collection(db, this.jobsCollection);

            // Add status filter only if status is not null
            if (status !== null && status !== undefined) {
                q = query(q, where('status', '==', status));
            }

            // Add additional filters
            Object.entries(filters).forEach(([field, value]) => {
                if (value !== null && value !== undefined && value !== '') {
                    q = query(q, where(field, '==', value));
                }
            });

            // Add ordering
            q = query(q, orderBy(orderByField, orderDirection));

            // Add limit
            if (limitCount && limitCount > 0) {
                q = query(q, limit(limitCount));
            }

            // Add timeout to prevent hanging
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Firebase query timeout')), 10000); // 10 second timeout
            });

            const queryPromise = getDocs(q);
            const querySnapshot = await Promise.race([queryPromise, timeoutPromise]);

            const jobs = [];

            querySnapshot.forEach((doc) => {
                const data = doc.data();
                jobs.push({
                    id: doc.id,
                    ...data,
                    createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
                    updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt
                });
            });

            console.log(`Successfully loaded ${jobs.length} jobs from Firebase`);
            return jobs;
        } catch (error) {
            console.error('Error getting jobs:', error);
            console.error('Error details:', {
                message: error.message,
                code: error.code,
                stack: error.stack
            });

            // Re-throw with more context
            throw new Error(`Failed to load jobs: ${error.message}`);
        }
    }

    /**
     * Update a job in Firestore
     * @param {string} jobId 
     * @param {Object} updateData 
     * @returns {Promise}
     */
    async updateJob(jobId, updateData) {
        try {
            const jobRef = doc(db, this.jobsCollection, jobId);
            const updateWithTimestamp = {
                ...updateData,
                updatedAt: new Date(),
                updatedBy: auth.currentUser?.email || 'admin'
            };
            
            await updateDoc(jobRef, updateWithTimestamp);
            return { id: jobId, ...updateWithTimestamp };
        } catch (error) {
            console.error('Error updating job:', error);
            throw error;
        }
    }

    /**
     * Delete a job from Firestore
     * @param {string} jobId 
     * @returns {Promise}
     */
    async deleteJob(jobId) {
        try {
            await deleteDoc(doc(db, this.jobsCollection, jobId));
            return jobId;
        } catch (error) {
            console.error('Error deleting job:', error);
            throw error;
        }
    }

    /**
     * Search jobs by keywords
     * @param {string} searchTerm 
     * @param {Object} filters 
     * @returns {Promise}
     */
    async searchJobs(searchTerm, filters = {}) {
        try {
            // Get all active jobs first (Firestore doesn't support full-text search natively)
            const jobs = await this.getJobs({ status: 'active', ...filters });
            
            if (!searchTerm) return jobs;

            // Filter jobs based on search term
            const searchTermLower = searchTerm.toLowerCase();
            return jobs.filter(job => {
                const searchableText = [
                    job.title,
                    job.company,
                    job.description,
                    job.location,
                    job.category,
                    job.skills?.join(' ')
                ].join(' ').toLowerCase();
                
                return searchableText.includes(searchTermLower);
            });
        } catch (error) {
            console.error('Error searching jobs:', error);
            throw error;
        }
    }

    /**
     * Get job statistics for admin dashboard
     * @returns {Promise}
     */
    async getJobStats() {
        try {
            const allJobs = await this.getJobs({ status: null }); // Get all jobs regardless of status

            const stats = {
                total: allJobs.length,
                active: allJobs.filter(job => job.status === 'active').length,
                inactive: allJobs.filter(job => job.status === 'inactive').length,
                draft: allJobs.filter(job => job.status === 'draft').length,
                totalViews: allJobs.reduce((sum, job) => sum + (job.views || 0), 0),
                totalApplications: allJobs.reduce((sum, job) => sum + (job.applications || 0), 0),
                recentJobs: allJobs.slice(0, 5) // Last 5 jobs
            };

            return stats;
        } catch (error) {
            console.error('Error getting job stats:', error);
            throw error;
        }
    }

    /**
     * Test Firebase connection
     * @returns {Promise}
     */
    async testConnection() {
        try {
            console.log('Testing Firebase connection...');
            const startTime = Date.now();

            // Try to read from the jobs collection with a limit of 1
            const q = query(collection(db, this.jobsCollection), limit(1));
            const querySnapshot = await getDocs(q);

            const endTime = Date.now();
            const responseTime = endTime - startTime;

            console.log(`Firebase connection test successful. Response time: ${responseTime}ms`);

            return {
                success: true,
                responseTime,
                hasData: !querySnapshot.empty,
                message: `Connection successful (${responseTime}ms)`
            };
        } catch (error) {
            console.error('Firebase connection test failed:', error);
            return {
                success: false,
                error: error.message,
                message: `Connection failed: ${error.message}`
            };
        }
    }
}

/**
 * Utility Functions
 */
class FirebaseUtils {
    /**
     * Show notification message
     * @param {string} message 
     * @param {string} type 
     * @param {number} duration 
     */
    static showNotification(message, type = 'info', duration = 5000) {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.firebase-notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `firebase-notification fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        
        notification.innerHTML = `
            <div class="flex items-center space-x-2">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-circle' :
                    type === 'warning' ? 'fa-exclamation-triangle' :
                    'fa-info-circle'
                }"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after specified duration
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
    }

    /**
     * Format date for display
     * @param {Date} date 
     * @returns {string}
     */
    static formatDate(date) {
        if (!date) return 'N/A';
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Validate email format
     * @param {string} email 
     * @returns {boolean}
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Generate unique ID
     * @returns {string}
     */
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}

// Create instances
const firebaseAuth = new FirebaseAuthManager();
const firebaseJobs = new FirebaseJobManager();

// Create global instances for backward compatibility
window.firebaseAuth = firebaseAuth;
window.firebaseJobs = firebaseJobs;
window.FirebaseUtils = FirebaseUtils;

// Export for module usage
export { firebaseAuth, firebaseJobs, FirebaseUtils, ADMIN_EMAIL };