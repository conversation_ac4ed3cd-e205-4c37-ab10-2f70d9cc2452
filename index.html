<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USA EasyNaukri4U - Find Your Dream Job in America</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">


    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'space': ['Space Grotesk', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                        'cyber': '#00d4ff',
                        'neon': '#ff0080',
                        'electric': '#7c3aed',
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'gradient': 'gradient 8s ease infinite',
                        'shimmer': 'shimmer 2s linear infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 20px rgba(37, 99, 235, 0.5)' },
                            '100%': { boxShadow: '0 0 40px rgba(37, 99, 235, 0.8)' },
                        },
                        gradient: {
                            '0%, 100%': { backgroundPosition: '0% 50%' },
                            '50%': { backgroundPosition: '100% 50%' },
                        },
                        shimmer: {
                            '0%': { transform: 'translateX(-100%)' },
                            '100%': { transform: 'translateX(100%)' },
                        },
                    },
                    backgroundSize: {
                        '300%': '300%',
                    },
                }
            }
        }
    </script>

    <style>
        /* Custom CSS for advanced effects */
        .glass-morphism {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.9);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .glass-dark {
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
        }

        .gradient-mesh {
            background: linear-gradient(45deg, #2563eb, #7c3aed, #ec4899, #f59e0b);
            background-size: 400% 400%;
            animation: gradient 8s ease infinite;
        }

        .cyber-grid {
            background-image:
                linear-gradient(rgba(37, 99, 235, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(37, 99, 235, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
        }

        .neon-glow {
            box-shadow:
                0 0 20px rgba(37, 99, 235, 0.5),
                0 0 20px rgba(37, 99, 235, 0.3),
                0 0 20px rgba(37, 99, 235, 0.1);
        }

        .text-glow {
            text-shadow:
                0 0 10px rgba(37, 99, 235, 0.8),
                0 0 20px rgba(37, 99, 235, 0.6),
                0 0 30px rgba(37, 99, 235, 0.4);
        }

        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .hover-lift:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .parallax-bg {
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
        }

        .shimmer-effect {
            position: relative;
            overflow: hidden;
        }

        .shimmer-effect::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
        }

        .floating-orb {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(37, 99, 235, 0.3), rgba(124, 58, 237, 0.3));
            filter: blur(40px);
            animation: float 8s ease-in-out infinite;
        }

        .hero-bg {
            background:
                radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.08) 0%, transparent 50%),
                linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }

        @media (prefers-reduced-motion: reduce) {
            .animate-float,
            .animate-glow,
            .animate-pulse-slow,
            .animate-gradient,
            .animate-shimmer {
                animation: none;
            }
        }
    </style>
    <!-- adsense -->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>
</head>
<body class="font-space bg-gray-50 text-gray-900 overflow-x-hidden">
    <!-- Floating Background Orbs -->
    <div class="fixed inset-0 pointer-events-none z-0">
        <div class="floating-orb w-96 h-96 top-10 left-10 animate-float" style="animation-delay: 0s;"></div>
        <div class="floating-orb w-64 h-64 top-1/2 right-20 animate-float" style="animation-delay: 2s;"></div>
        <div class="floating-orb w-80 h-80 bottom-20 left-1/3 animate-float" style="animation-delay: 4s;"></div>
    </div>

    <!-- Navigation -->
    <nav class="glass-morphism sticky top-0 z-50 border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-3 group">
                        <div class="relative">
                            <div class="bg-gradient-to-br from-primary to-electric p-3 rounded-xl group-hover:animate-pulse">
                                <i class="fas fa-briefcase text-2xl text-white"></i>
                            </div>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-blue-800 bg-clip-text text-transparent">USA.EasyNaukri4U</span>
                            <span class="text-xs text-blue-600 font-medium tracking-wider">NEXT-GEN CAREERS</span>
                        </div>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="relative text-blue-600 font-semibold group">
                        <span class="relative z-10">Home</span>
                        <div class="absolute inset-0 bg-gradient-to-r from-primary/10 to-electric/10 rounded-lg scale-0 group-hover:scale-100 transition-transform"></div>
                    </a>
                    <a href="jobs.html" class="relative text-gray-700 hover:text-blue-600 transition-all duration-300 group">
                        <span class="relative z-10">Find Jobs</span>
                        <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-electric group-hover:w-full transition-all duration-300"></div>
                    </a>
                    <a href="study-material.html" class="relative text-gray-700 hover:text-blue-600 transition-all duration-300 group">
                        <span class="relative z-10">Study Materials</span>
                        <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-electric group-hover:w-full transition-all duration-300"></div>
                    </a>
                    <a href="resume-builder.html" class="relative text-gray-700 hover:text-blue-600 transition-all duration-300 group">
                        <span class="relative z-10">Resume Builder</span>
                        <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-electric group-hover:w-full transition-all duration-300"></div>
                    </a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-blue-600 transition-colors p-2">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden glass-dark border-t border-gray-200">
            <div class="px-4 py-4 space-y-3">
                <a href="index.html" class="block py-3 text-blue-600 font-semibold">Home</a>
                <a href="jobs.html" class="block py-3 text-gray-700 hover:text-blue-600 transition-colors">Find Jobs</a>
                <a href="study-material.html" class="block py-3 text-gray-700 hover:text-blue-600 transition-colors">Study Materials</a>
                <a href="resume-builder.html" class="block py-3 text-gray-700 hover:text-blue-600 transition-colors">Resume Builder</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative min-h-screen hero-bg cyber-grid overflow-hidden">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-10 w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
            <div class="absolute top-40 right-20 w-1 h-1 bg-electric rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute bottom-40 left-1/4 w-1.5 h-1.5 bg-accent rounded-full animate-pulse" style="animation-delay: 2s;"></div>
            <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-blue-500 rounded-full animate-pulse" style="animation-delay: 3s;"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-32">
            <div class="text-center">
                <!-- Main Heading with Glow Effect -->
                <div class="mb-8 animate-float">
                    <h1 class="text-5xl md:text-8xl font-black mb-6 leading-tight">
                        <span class="block bg-gradient-to-r from-gray-900 via-blue-800 to-blue-600 bg-clip-text text-transparent">
                            Find Your Dream Job
                        </span>
                        <span class="block bg-gradient-to-r from-accent via-orange-500 to-red-500 bg-clip-text text-transparent">
                            in America
                        </span>
                    </h1>
                    <div class="w-32 h-1 bg-gradient-to-r from-primary to-electric mx-auto rounded-full animate-pulse"></div>
                </div>

                <p class="text-xl md:text-3xl mb-12 text-gray-700 font-light max-w-4xl mx-auto leading-relaxed">
                    Connect with top employers across the United States. Your career journey starts here.
                </p>

                <!-- Futuristic Search Bar -->
                <div class="max-w-5xl mx-auto mb-16">
                    <div class="glass-morphism rounded-2xl p-8 neon-glow hover-lift">
                        <div class="flex flex-col lg:flex-row gap-6">
                            <div class="flex-1 relative group">
                                <div class="absolute inset-0 bg-gradient-to-r from-primary/20 to-electric/20 rounded-xl blur-lg group-hover:blur-xl transition-all"></div>
                                <div class="relative">
                                    <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-blue-500 text-lg"></i>
                                    <input type="text" placeholder="Job title, keywords, or company"
                                           class="search-input w-full pl-12 pr-4 py-4 bg-white/80 text-gray-900 rounded-xl border border-blue-300/50 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/30 focus:outline-none transition-all placeholder-gray-500 text-lg">
                                </div>
                            </div>
                            <div class="flex-1 relative group">
                                <div class="absolute inset-0 bg-gradient-to-r from-electric/20 to-primary/20 rounded-xl blur-lg group-hover:blur-xl transition-all"></div>
                                <div class="relative">
                                    <i class="fas fa-map-marker-alt absolute left-4 top-1/2 transform -translate-y-1/2 text-blue-500 text-lg"></i>
                                    <input type="text" placeholder="City, state, or zip code"
                                           class="search-input w-full pl-12 pr-4 py-4 bg-white/80 text-gray-900 rounded-xl border border-blue-300/50 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/30 focus:outline-none transition-all placeholder-gray-500 text-lg">
                                </div>
                            </div>
                            <button class="search-btn relative group px-8 py-4 bg-gradient-to-r from-accent to-orange-500 text-white rounded-xl font-bold text-lg hover:from-orange-500 hover:to-accent transition-all duration-300 transform hover:scale-105 shimmer-effect">
                                <span class="relative z-10 flex items-center">
                                    <i class="fas fa-rocket mr-3"></i>Launch Search
                                </span>
                                <div class="absolute inset-0 bg-gradient-to-r from-accent to-orange-500 rounded-xl blur-lg opacity-50 group-hover:opacity-75 transition-opacity"></div>
                            </button>
                        </div>

                        <!-- Futuristic Quick Filters -->
                        <div class="flex flex-wrap gap-3 mt-8 justify-center">
                            <span class="quick-filter glass-dark px-4 py-2 rounded-full text-sm cursor-pointer hover:bg-primary/10 transition-all border border-blue-300/50 text-gray-700 hover:text-blue-600 hover:border-blue-500 hover:shadow-lg hover:shadow-blue-500/25">Remote</span>
                            <span class="quick-filter glass-dark px-4 py-2 rounded-full text-sm cursor-pointer hover:bg-primary/10 transition-all border border-blue-300/50 text-gray-700 hover:text-blue-600 hover:border-blue-500 hover:shadow-lg hover:shadow-blue-500/25">Full-time</span>
                            <span class="quick-filter glass-dark px-4 py-2 rounded-full text-sm cursor-pointer hover:bg-primary/10 transition-all border border-blue-300/50 text-gray-700 hover:text-blue-600 hover:border-blue-500 hover:shadow-lg hover:shadow-blue-500/25">Part-time</span>
                            <span class="quick-filter glass-dark px-4 py-2 rounded-full text-sm cursor-pointer hover:bg-primary/10 transition-all border border-blue-300/50 text-gray-700 hover:text-blue-600 hover:border-blue-500 hover:shadow-lg hover:shadow-blue-500/25">Contract</span>
                            <span class="quick-filter glass-dark px-4 py-2 rounded-full text-sm cursor-pointer hover:bg-primary/10 transition-all border border-blue-300/50 text-gray-700 hover:text-blue-600 hover:border-blue-500 hover:shadow-lg hover:shadow-blue-500/25">Internship</span>
                        </div>
                    </div>
                </div>

                <!-- Floating Action Elements -->
                <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                    <div class="flex items-center space-x-4 text-gray-600">
                        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-lg font-medium">Live Job Updates</span>
                    </div>
                    <div class="flex items-center space-x-4 text-gray-600">
                        <div class="w-3 h-3 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
                        <span class="text-lg font-medium">AI-Powered Matching</span>
                    </div>
                    <div class="flex items-center space-x-4 text-gray-600">
                        <div class="w-3 h-3 bg-purple-400 rounded-full animate-pulse" style="animation-delay: 2s;"></div>
                        <span class="text-lg font-medium">Instant Applications</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div class="w-6 h-10 border-2 border-blue-400 rounded-full flex justify-center">
                <div class="w-1 h-3 bg-blue-400 rounded-full mt-2 animate-pulse"></div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-24 bg-white relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.3) 1px, transparent 0); background-size: 40px 40px;"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-12 text-center">
                <div class="group hover-lift">
                    <div class="glass-morphism rounded-2xl p-8 border border-blue-200 hover:border-blue-300 transition-all duration-300">
                        <div class="relative">
                            <div class="text-5xl md:text-6xl font-black bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent mb-4 animate-pulse-slow">50K+</div>
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-blue-600/10 rounded-full blur-2xl group-hover:blur-3xl transition-all"></div>
                        </div>
                        <div class="text-gray-700 text-xl font-semibold mb-2">Active Jobs</div>
                        <div class="w-16 h-1 bg-gradient-to-r from-blue-400 to-blue-600 mx-auto rounded-full"></div>
                    </div>
                </div>

                <div class="group hover-lift">
                    <div class="glass-morphism rounded-2xl p-8 border border-purple-200 hover:border-purple-300 transition-all duration-300">
                        <div class="relative">
                            <div class="text-5xl md:text-6xl font-black bg-gradient-to-r from-electric to-purple-600 bg-clip-text text-transparent mb-4 animate-pulse-slow" style="animation-delay: 1s;">25K+</div>
                            <div class="absolute inset-0 bg-gradient-to-r from-purple-400/10 to-purple-600/10 rounded-full blur-2xl group-hover:blur-3xl transition-all"></div>
                        </div>
                        <div class="text-gray-700 text-xl font-semibold mb-2">Companies</div>
                        <div class="w-16 h-1 bg-gradient-to-r from-electric to-purple-600 mx-auto rounded-full"></div>
                    </div>
                </div>

                <div class="group hover-lift">
                    <div class="glass-morphism rounded-2xl p-8 border border-orange-200 hover:border-orange-300 transition-all duration-300">
                        <div class="relative">
                            <div class="text-5xl md:text-6xl font-black bg-gradient-to-r from-accent to-orange-600 bg-clip-text text-transparent mb-4 animate-pulse-slow" style="animation-delay: 2s;">100K+</div>
                            <div class="absolute inset-0 bg-gradient-to-r from-accent/10 to-orange-600/10 rounded-full blur-2xl group-hover:blur-3xl transition-all"></div>
                        </div>
                        <div class="text-gray-700 text-xl font-semibold mb-2">Job Seekers</div>
                        <div class="w-16 h-1 bg-gradient-to-r from-accent to-orange-600 mx-auto rounded-full"></div>
                    </div>
                </div>
            </div>

            <!-- Additional Floating Stats -->
            <div class="mt-16 flex flex-wrap justify-center gap-8">
                <div class="flex items-center space-x-3 glass-dark px-6 py-3 rounded-full border border-gray-200">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span class="text-green-600 font-medium">98% Success Rate</span>
                </div>
                <div class="flex items-center space-x-3 glass-dark px-6 py-3 rounded-full border border-gray-200">
                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
                    <span class="text-blue-600 font-medium">24/7 Support</span>
                </div>
                <div class="flex items-center space-x-3 glass-dark px-6 py-3 rounded-full border border-gray-200">
                    <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style="animation-delay: 2s;"></div>
                    <span class="text-purple-600 font-medium">AI-Powered</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Jobs Section -->
    <section class="py-24 bg-gray-50 relative">
        <!-- Animated Background -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-primary/5 to-electric/5 rounded-full blur-3xl animate-float"></div>
            <div class="absolute bottom-0 right-1/4 w-80 h-80 bg-gradient-to-r from-accent/5 to-orange-400/5 rounded-full blur-3xl animate-float" style="animation-delay: 3s;"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <div class="inline-block mb-6">
                    <span class="text-blue-600 text-sm font-bold tracking-wider uppercase bg-blue-100 px-4 py-2 rounded-full border border-blue-200">
                        ⚡ Live Opportunities
                    </span>
                </div>
                <h2 class="text-4xl md:text-6xl font-black mb-6">
                    <span class="bg-gradient-to-r from-gray-900 via-blue-700 to-blue-600 bg-clip-text text-transparent">
                        Featured Jobs
                    </span>
                </h2>
                <p class="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Discover opportunities from top companies with our AI-powered job matching
                </p>
                <div class="w-24 h-1 bg-gradient-to-r from-primary to-electric mx-auto mt-6 rounded-full animate-pulse"></div>
            </div>

            <!-- Dynamic Jobs Container -->
            <div id="jobs-container" class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- Loading placeholder -->
                <div class="col-span-full text-center py-16">
                    <div class="relative inline-block">
                        <div class="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                        <div class="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-electric rounded-full animate-spin" style="animation-direction: reverse; animation-duration: 1.5s;"></div>
                    </div>
                    <p class="mt-6 text-gray-600 text-lg font-medium">Loading latest opportunities...</p>
                    <div class="flex justify-center space-x-2 mt-4">
                        <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        <div class="w-2 h-2 bg-electric rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
                        <div class="w-2 h-2 bg-accent rounded-full animate-pulse" style="animation-delay: 1s;"></div>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <a href="jobs.html" class="group relative inline-flex items-center">
                    <div class="absolute inset-0 bg-gradient-to-r from-primary to-electric rounded-2xl blur-lg opacity-50 group-hover:opacity-75 transition-opacity"></div>
                    <div class="relative glass-morphism border border-blue-300 text-gray-900 px-8 py-4 rounded-2xl hover:border-blue-500 transition-all duration-300 font-semibold text-lg">
                        <span class="flex items-center">
                            <i class="fas fa-rocket mr-3 text-blue-600"></i>
                            Explore All Opportunities
                            <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform"></i>
                        </span>
                    </div>
                </a>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="py-24 bg-white relative overflow-hidden">
        <!-- Geometric Background -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-10 w-32 h-32 border border-blue-200 rotate-45 animate-float"></div>
            <div class="absolute bottom-20 right-20 w-24 h-24 border border-purple-200 rotate-12 animate-float" style="animation-delay: 2s;"></div>
            <div class="absolute top-1/2 left-1/4 w-16 h-16 border border-orange-200 -rotate-12 animate-float" style="animation-delay: 4s;"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <div class="inline-block mb-6">
                    <span class="text-purple-600 text-sm font-bold tracking-wider uppercase bg-purple-100 px-4 py-2 rounded-full border border-purple-200">
                        🎯 Career Paths
                    </span>
                </div>
                <h2 class="text-4xl md:text-6xl font-black mb-6">
                    <span class="bg-gradient-to-r from-gray-900 via-purple-700 to-electric bg-clip-text text-transparent">
                        Browse by Category
                    </span>
                </h2>
                <p class="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Find jobs in your field of expertise with our intelligent categorization
                </p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
                <div class="group cursor-pointer hover-lift">
                    <div class="glass-morphism rounded-2xl p-6 text-center border border-blue-200 hover:border-blue-300 transition-all duration-300">
                        <div class="relative mb-4">
                            <div class="w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-code text-3xl text-blue-600 group-hover:text-blue-700 transition-colors"></i>
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-blue-600/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all"></div>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2 text-lg">Technology</h3>
                        <p class="text-gray-600 text-sm font-medium">12,450 jobs</p>
                        <div class="w-12 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600 mx-auto mt-3 rounded-full"></div>
                    </div>
                </div>

                <div class="group cursor-pointer hover-lift">
                    <div class="glass-morphism rounded-2xl p-6 text-center border border-green-200 hover:border-green-300 transition-all duration-300">
                        <div class="relative mb-4">
                            <div class="w-20 h-20 bg-gradient-to-br from-green-100 to-green-200 rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-chart-line text-3xl text-green-600 group-hover:text-green-700 transition-colors"></i>
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-br from-green-400/10 to-green-600/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all"></div>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2 text-lg">Finance</h3>
                        <p class="text-gray-600 text-sm font-medium">8,230 jobs</p>
                        <div class="w-12 h-0.5 bg-gradient-to-r from-green-400 to-green-600 mx-auto mt-3 rounded-full"></div>
                    </div>
                </div>

                <div class="group cursor-pointer hover-lift">
                    <div class="glass-morphism rounded-2xl p-6 text-center border border-red-200 hover:border-red-300 transition-all duration-300">
                        <div class="relative mb-4">
                            <div class="w-20 h-20 bg-gradient-to-br from-red-100 to-red-200 rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-heartbeat text-3xl text-red-600 group-hover:text-red-700 transition-colors"></i>
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-br from-red-400/10 to-red-600/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all"></div>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2 text-lg">Healthcare</h3>
                        <p class="text-gray-600 text-sm font-medium">6,890 jobs</p>
                        <div class="w-12 h-0.5 bg-gradient-to-r from-red-400 to-red-600 mx-auto mt-3 rounded-full"></div>
                    </div>
                </div>

                <div class="group cursor-pointer hover-lift">
                    <div class="glass-morphism rounded-2xl p-6 text-center border border-purple-200 hover:border-purple-300 transition-all duration-300">
                        <div class="relative mb-4">
                            <div class="w-20 h-20 bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-bullhorn text-3xl text-purple-600 group-hover:text-purple-700 transition-colors"></i>
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-br from-purple-400/10 to-purple-600/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all"></div>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2 text-lg">Marketing</h3>
                        <p class="text-gray-600 text-sm font-medium">5,670 jobs</p>
                        <div class="w-12 h-0.5 bg-gradient-to-r from-purple-400 to-purple-600 mx-auto mt-3 rounded-full"></div>
                    </div>
                </div>

                <div class="group cursor-pointer hover-lift">
                    <div class="glass-morphism rounded-2xl p-6 text-center border border-yellow-200 hover:border-yellow-300 transition-all duration-300">
                        <div class="relative mb-4">
                            <div class="w-20 h-20 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-graduation-cap text-3xl text-yellow-600 group-hover:text-yellow-700 transition-colors"></i>
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-br from-yellow-400/10 to-yellow-600/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all"></div>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2 text-lg">Education</h3>
                        <p class="text-gray-600 text-sm font-medium">4,320 jobs</p>
                        <div class="w-12 h-0.5 bg-gradient-to-r from-yellow-400 to-yellow-600 mx-auto mt-3 rounded-full"></div>
                    </div>
                </div>

                <div class="group cursor-pointer hover-lift">
                    <div class="glass-morphism rounded-2xl p-6 text-center border border-indigo-200 hover:border-indigo-300 transition-all duration-300">
                        <div class="relative mb-4">
                            <div class="w-20 h-20 bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-palette text-3xl text-indigo-600 group-hover:text-indigo-700 transition-colors"></i>
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-br from-indigo-400/10 to-indigo-600/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all"></div>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2 text-lg">Design</h3>
                        <p class="text-gray-600 text-sm font-medium">3,450 jobs</p>
                        <div class="w-12 h-0.5 bg-gradient-to-r from-indigo-400 to-indigo-600 mx-auto mt-3 rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-24 bg-gray-50 relative overflow-hidden">
        <!-- Dynamic Background -->
        <div class="absolute inset-0">
            <div class="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-primary/3 to-transparent"></div>
            <div class="absolute bottom-0 left-0 w-1/2 h-full bg-gradient-to-r from-electric/3 to-transparent"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <div class="inline-block mb-6">
                    <span class="text-orange-600 text-sm font-bold tracking-wider uppercase bg-orange-100 px-4 py-2 rounded-full border border-orange-200">
                        🚀 Power Tools
                    </span>
                </div>
                <h2 class="text-4xl md:text-6xl font-black mb-6 leading-tight">
                    <span class="bg-gradient-to-r from-gray-900 via-orange-700 to-accent bg-clip-text text-transparent">
                        Everything You Need to Land
                    </span>
                    <br>
                    <span class="bg-gradient-to-r from-accent via-orange-600 to-red-600 bg-clip-text text-transparent">
                        Your Dream Job
                    </span>
                </h2>
                <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                    Comprehensive tools and resources to accelerate your career with cutting-edge technology
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-12 max-w-6xl mx-auto">
                <!-- Resume Builder Feature -->
                <div class="group hover-lift">
                    <div class="glass-morphism rounded-3xl p-10 border border-blue-200 hover:border-blue-300 transition-all duration-500 relative overflow-hidden">
                        <!-- Animated Background -->
                        <div class="absolute inset-0 group-hover:from-blue-100 group-hover:to-cyan-100 transition-all duration-500"></div>

                        <div class="relative z-10">
                            <div class="flex items-center justify-center mb-8">
                                <div class="relative">
                                    <div class="w-24 h-24 bg-gradient-to-br from-blue-100 to-cyan-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                        <i class="fas fa-file-alt text-4xl text-blue-600"></i>
                                    </div>
                                    <div class="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-cyan-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all"></div>
                                </div>
                            </div>

                            <h3 class="text-2xl font-black text-gray-900 mb-6 text-center">Professional Resume Builder</h3>
                            <p class="text-gray-600 mb-8 text-center leading-relaxed">Create stunning, ATS-friendly resumes with our AI-powered builder. Choose from professional templates and get hired faster.</p>

                            <div class="space-y-4 mb-8">
                                <div class="flex items-center group/item">
                                    <div class="w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-4 group-hover/item:scale-110 transition-transform">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                    <span class="text-gray-700 font-medium">ATS-Optimized Templates</span>
                                </div>
                                <div class="flex items-center group/item">
                                    <div class="w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-4 group-hover/item:scale-110 transition-transform">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                    <span class="text-gray-700 font-medium">Real-time AI Preview</span>
                                </div>
                                <div class="flex items-center group/item">
                                    <div class="w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-4 group-hover/item:scale-110 transition-transform">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                    <span class="text-gray-700 font-medium">Instant PDF Export</span>
                                </div>
                                <div class="flex items-center group/item">
                                    <div class="w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-4 group-hover/item:scale-110 transition-transform">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                    <span class="text-gray-700 font-medium">Industry-Specific Formats</span>
                                </div>
                            </div>

                            <div class="text-center">
                                <a href="resume-builder.html" class="group/btn relative inline-flex items-center">
                                    <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl blur-lg opacity-0 group-hover/btn:opacity-75 transition-opacity"></div>
                                    <div class="relative bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-cyan-500 hover:to-blue-500 transition-all duration-300">
                                        <span class="flex items-center">
                                            <i class="fas fa-magic mr-3"></i>
                                            Build Resume
                                            <i class="fas fa-arrow-right ml-3 group-hover/btn:translate-x-1 transition-transform"></i>
                                        </span>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Study Materials Feature -->
                <div class="group hover-lift">
                    <div class="glass-morphism rounded-3xl p-10 border border-blue-200 hover:border-blue-300 transition-all duration-500 relative overflow-hidden">
                        <!-- Animated Background -->
                        <div class="absolute inset-0 group-hover:from-blue-100 group-hover:to-cyan-100 transition-all duration-500"></div>

                        <div class="relative z-10">
                            <div class="flex items-center justify-center mb-8">
                                <div class="relative">
                                    <div class="w-24 h-24 bg-gradient-to-br from-blue-100 to-cyan-200 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                        <i class="fas fa-graduation-cap text-4xl text-green-700"></i>
                                    </div>
                                    <div class="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-cyan-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all"></div>
                                </div>
                            </div>

                            <h3 class="text-2xl font-black text-gray-900 mb-6 text-center">Study Materials & Prep</h3>
                            <p class="text-gray-600 mb-8 text-center leading-relaxed">Master interview skills with our comprehensive study materials, AI-powered practice tests, and virtual mock interviews.</p>

                            <div class="space-y-4 mb-8">
                                <div class="flex items-center group/item">
                                    <div class="w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-4 group-hover/item:scale-110 transition-transform">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                    <span class="text-gray-700 font-medium">500+ AI Practice Questions</span>
                                </div>
                                <div class="flex items-center group/item">
                                    <div class="w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-4 group-hover/item:scale-110 transition-transform">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                    <span class="text-gray-700 font-medium">Virtual Mock Interviews</span>
                                </div>
                                <div class="flex items-center group/item">
                                    <div class="w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-4 group-hover/item:scale-110 transition-transform">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                    <span class="text-gray-700 font-medium">Technical & Soft Skills</span>
                                </div>
                                <div class="flex items-center group/item">
                                    <div class="w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-4 group-hover/item:scale-110 transition-transform">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                    <span class="text-gray-700 font-medium">Interactive Video Tutorials</span>
                                </div>
                            </div>

                            <div class="text-center">
                                <a href="resume-builder.html" class="group/btn relative inline-flex items-center">
                                    <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl blur-lg opacity-0 group-hover/btn:opacity-75 transition-opacity"></div>
                                    <div class="relative bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-cyan-500 hover:to-blue-500 transition-all duration-300">
                                        <span class="flex items-center">
                                            <i class="fas fa-brain mr-3"></i>
                                            Start Learning
                                            <i class="fas fa-arrow-right ml-3 group-hover/btn:translate-x-1 transition-transform"></i>
                                        </span>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="py-10 bg-white relative overflow-hidden">
        <!-- Connecting Lines Background -->
        <div class="absolute inset-0 hidden md:block">
            <svg class="w-full h-full" viewBox="0 0 1200 400" fill="none">
                <path d="M200 200 Q400 100 600 200 T1000 200" stroke="url(#gradient)" stroke-width="2" stroke-dasharray="5,5" opacity="0.3">
                    <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite"/>
                </path>
                <defs>
                    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#2563eb"/>
                        <stop offset="50%" style="stop-color:#7c3aed"/>
                        <stop offset="100%" style="stop-color:#f59e0b"/>
                    </linearGradient>
                </defs>
            </svg>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <div class="inline-block mb-6">
                    <span class="text-blue-600 text-sm font-bold tracking-wider uppercase bg-blue-100 px-4 py-2 rounded-full border border-blue-200">
                        ⚡ Simple Process
                    </span>
                </div>
                <h2 class="text-4xl md:text-6xl font-black mb-6">
                    <span class="bg-gradient-to-r from-gray-900 via-blue-700 to-primary bg-clip-text text-transparent">
                        How It Works
                    </span>
                </h2>
                <p class="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Get hired in 4 simple steps with our AI-powered platform
                </p>
            </div>

            <div class="grid md:grid-cols-4 gap-8 lg:gap-12">
                <div class="group text-center hover-lift">
                    <div class="relative mb-8">
                        <div class="glass-morphism w-24 h-24 rounded-full flex items-center justify-center mx-auto border border-primary/30 group-hover:border-primary/50 transition-all duration-300">
                            <div class="relative">
                                <div class="text-3xl font-black bg-gradient-to-r from-primary to-cyan-400 bg-clip-text text-transparent">1</div>
                                <div class="absolute inset-0 bg-gradient-to-r from-primary/20 to-cyan-400/20 rounded-full blur-xl group-hover:blur-2xl transition-all"></div>
                            </div>
                        </div>
                        <!-- Floating particles -->
                        <div class="absolute top-0 left-1/2 w-2 h-2 bg-primary/50 rounded-full animate-pulse transform -translate-x-1/2 -translate-y-4"></div>
                    </div>
                    <h3 class="text-xl font-black text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">Create Your Profile</h3>
                    <p class="text-gray-600 leading-relaxed">Sign up and build your professional profile with our AI-powered tools and smart recommendations.</p>
                </div>

                <div class="group text-center hover-lift">
                    <div class="relative mb-8">
                        <div class="glass-morphism w-24 h-24 rounded-full flex items-center justify-center mx-auto border border-purple-200 group-hover:border-purple-300 transition-all duration-300">
                            <div class="relative">
                                <div class="text-3xl font-black bg-gradient-to-r from-electric to-purple-600 bg-clip-text text-transparent">2</div>
                                <div class="absolute inset-0 bg-gradient-to-r from-electric/10 to-purple-600/10 rounded-full blur-xl group-hover:blur-2xl transition-all"></div>
                            </div>
                        </div>
                        <!-- Floating particles -->
                        <div class="absolute top-0 left-1/2 w-2 h-2 bg-electric rounded-full animate-pulse transform -translate-x-1/2 -translate-y-4" style="animation-delay: 0.5s;"></div>
                    </div>
                    <h3 class="text-xl font-black text-gray-900 mb-4 group-hover:text-purple-600 transition-colors">Search & Apply</h3>
                    <p class="text-gray-600 leading-relaxed">Browse thousands of jobs with AI matching and apply with one click using your optimized profile.</p>
                </div>

                <div class="group text-center hover-lift">
                    <div class="relative mb-8">
                        <div class="glass-morphism w-24 h-24 rounded-full flex items-center justify-center mx-auto border border-green-200 group-hover:border-green-300 transition-all duration-300">
                            <div class="relative">
                                <div class="text-3xl font-black bg-gradient-to-r from-green-500 to-emerald-600 bg-clip-text text-transparent">3</div>
                                <div class="absolute inset-0 bg-gradient-to-r from-green-400/10 to-emerald-500/10 rounded-full blur-xl group-hover:blur-2xl transition-all"></div>
                            </div>
                        </div>
                        <!-- Floating particles -->
                        <div class="absolute top-0 left-1/2 w-2 h-2 bg-green-400 rounded-full animate-pulse transform -translate-x-1/2 -translate-y-4" style="animation-delay: 1s;"></div>
                    </div>
                    <h3 class="text-xl font-black text-gray-900 mb-4 group-hover:text-green-600 transition-colors">Prepare & Practice</h3>
                    <p class="text-gray-600 leading-relaxed">Use our study materials and AI-powered mock interviews to ace your interviews with confidence.</p>
                </div>

                <div class="group text-center hover-lift">
                    <div class="relative mb-8">
                        <div class="glass-morphism w-24 h-24 rounded-full flex items-center justify-center mx-auto border border-orange-200 group-hover:border-orange-300 transition-all duration-300">
                            <div class="relative">
                                <div class="text-3xl font-black bg-gradient-to-r from-accent to-orange-600 bg-clip-text text-transparent">4</div>
                                <div class="absolute inset-0 bg-gradient-to-r from-accent/10 to-orange-500/10 rounded-full blur-xl group-hover:blur-2xl transition-all"></div>
                            </div>
                        </div>
                        <!-- Floating particles -->
                        <div class="absolute top-0 left-1/2 w-2 h-2 bg-accent rounded-full animate-pulse transform -translate-x-1/2 -translate-y-4" style="animation-delay: 1.5s;"></div>
                    </div>
                    <h3 class="text-xl font-black text-gray-900 mb-4 group-hover:text-orange-600 transition-colors">Get Hired</h3>
                    <p class="text-gray-600 leading-relaxed">Land your dream job and start your new career journey with our ongoing support and guidance.</p>
                </div>
            </div>

            <!-- Success Metrics -->
            <div class="mt-16 text-center">
                <div class="glass-dark rounded-2xl p-8 border border-white/10">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div class="flex items-center justify-center space-x-3">
                            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                            <span class="text-green-300 font-bold text-lg">Average 2 weeks to hire</span>
                        </div>
                        <div class="flex items-center justify-center space-x-3">
                            <div class="w-3 h-3 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
                            <span class="text-blue-300 font-bold text-lg">98% interview success rate</span>
                        </div>
                        <div class="flex items-center justify-center space-x-3">
                            <div class="w-3 h-3 bg-purple-400 rounded-full animate-pulse" style="animation-delay: 2s;"></div>
                            <span class="text-purple-300 font-bold text-lg">5x faster applications</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="py-10 bg-gray-50 relative overflow-hidden">
        <!-- Animated Grid Background -->
        <div class="absolute inset-0 cyber-grid opacity-20"></div>
        <div class="absolute inset-0">
            <div class="absolute top-1/4 left-10 w-64 h-64 bg-gradient-to-r from-primary/10 to-electric/10 rounded-full blur-3xl animate-float"></div>
            <div class="absolute bottom-1/4 right-10 w-48 h-48 bg-gradient-to-r from-accent/10 to-orange-400/10 rounded-full blur-3xl animate-float" style="animation-delay: 2s;"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <div class="inline-block mb-6">
                    <span class="text-cyan-600 text-sm font-bold tracking-wider uppercase bg-cyan-100 px-4 py-2 rounded-full border border-cyan-200">
                        🏆 Premium Platform
                    </span>
                </div>
                <h2 class="text-4xl md:text-6xl font-black mb-6 leading-tight">
                    <span class="bg-gradient-to-r from-gray-900 via-cyan-700 to-blue-600 bg-clip-text text-transparent">
                        Why Choose
                    </span>
                    <br>
                    <span class="bg-gradient-to-r from-cyan-600 via-blue-600 to-primary bg-clip-text text-transparent">
                        USA EasyNaukri4U?
                    </span>
                </h2>
                <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                    The most advanced and comprehensive job portal in America
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                <div class="group text-center hover-lift">
                    <div class="glass-morphism rounded-3xl p-8 border border-blue-300/20 hover:border-blue-300/40 transition-all duration-300 h-full">
                        <div class="relative mb-6">
                            <div class="w-24 h-24 bg-gradient-to-br from-blue-400/20 to-cyan-500/20 rounded-2xl flex items-center justify-center mx-auto group-hover:scale-100 transition-transform duration-300">
                                <i class="fas fa-shield-alt text-4xl text-blue-700"></i>
                            </div>
                        </div>
                        <h3 class="text-xl font-black text-black mb-4 group-hover:text-cyan-400 transition-colors">Trusted Platform</h3>
                        <p class="text-cyan-700 leading-relaxed">Verified companies and military-grade security. Your data is protected with advanced encryption.</p>
                        <div class="w-16 h-0.5 bg-gradient-to-r from-blue-300 to-cyan-400 mx-auto mt-4 rounded-full"></div>
                    </div>
                </div>

                <div class="group text-center hover-lift">
                    <div class="glass-morphism rounded-3xl p-8 border border-green-300/20 hover:border-green-300/40 transition-all duration-300 h-full">
                        <div class="relative mb-6">
                            <div class="w-24 h-24 bg-gradient-to-br from-green-400/20 to-emerald-500/20 rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-rocket text-4xl text-green-300"></i>
                            </div>
                        </div>
                        <h3 class="text-xl font-black text-black mb-4 group-hover:text-green-300 transition-colors">Lightning Fast</h3>
                        <p class="text-green-700 leading-relaxed">Get matched with relevant jobs instantly. Our quantum AI system finds the perfect fit in milliseconds.</p>
                        <div class="w-16 h-0.5 bg-gradient-to-r from-green-300 to-emerald-400 mx-auto mt-4 rounded-full"></div>
                    </div>
                </div>

                <div class="group text-center hover-lift">
                    <div class="glass-morphism rounded-3xl p-8 border border-purple-300/20 hover:border-purple-300/40 transition-all duration-300 h-full">
                        <div class="relative mb-6">
                            <div class="w-24 h-24 bg-gradient-to-br from-purple-400/20 to-electric/20 rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-users text-4xl text-purple-300"></i>
                            </div>
                        </div>
                        <h3 class="text-xl font-black text-black mb-4 group-hover:text-purple-300 transition-colors">Expert Support</h3>
                        <p class="text-purple-700 leading-relaxed">24/7 AI-powered support and career guidance from industry experts and career coaches.</p>
                        <div class="w-16 h-0.5 bg-gradient-to-r from-purple-300 to-electric mx-auto mt-4 rounded-full"></div>
                    </div>
                </div>

                <div class="group text-center hover-lift">
                    <div class="glass-morphism rounded-3xl p-8 border border-orange-300/20 hover:border-orange-300/40 transition-all duration-300 h-full">
                        <div class="relative mb-6">
                            <div class="w-24 h-24 bg-gradient-to-br from-orange-400/20 to-accent/20 rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-chart-line text-4xl text-orange-300"></i>
                            </div>
                        </div>
                        <h3 class="text-xl font-black text-black mb-4 group-hover:text-orange-300 transition-colors">Career Growth</h3>
                        <p class="text-orange-700 leading-relaxed">Continuous learning resources and AI-driven career development tools to accelerate your growth.</p>
                        <div class="w-16 h-0.5 bg-gradient-to-r from-orange-300 to-accent mx-auto mt-4 rounded-full"></div>
                    </div>
                </div>
            </div>

            <!-- Additional Features Grid -->
            <div class="grid md:grid-cols-3 gap-8">
                <div class="group hover-lift">
                    <div class="glass-dark rounded-2xl p-8 border border-white/10 hover:border-cyan-300/30 transition-all duration-300 h-full">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-gradient-to-br from-primary/20 to-cyan-500/20 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <i class="fas fa-mobile-alt text-2xl text-cyan-600"></i>
                            </div>
                            <h4 class="text-xl font-bold text-black group-hover:text-cyan-300 transition-colors">Mobile Optimized</h4>
                        </div>
                        <p class="text-cyan-700 leading-relaxed">Search and apply for jobs on the go with our cutting-edge mobile platform and progressive web app.</p>
                        <div class="flex items-center mt-4 space-x-2">
                            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <span class="text-green-300 text-sm font-medium">Native App Experience</span>
                        </div>
                    </div>
                </div>

                <div class="group hover-lift">
                    <div class="glass-dark rounded-2xl p-8 border border-white/10 hover:border-electric/30 transition-all duration-300 h-full">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-gradient-to-br from-electric/20 to-purple-500/20 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <i class="fas fa-bell text-2xl text-electric"></i>
                            </div>
                            <h4 class="text-xl font-bold text-black group-hover:text-electric transition-colors">Smart Alerts</h4>
                        </div>
                        <p class="text-purple-700 leading-relaxed">Get notified instantly with AI-powered alerts when new jobs matching your criteria are posted.</p>
                        <div class="flex items-center mt-4 space-x-2">
                            <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
                            <span class="text-blue-300 text-sm font-medium">Real-time Notifications</span>
                        </div>
                    </div>
                </div>

                <div class="group hover-lift">
                    <div class="glass-dark rounded-2xl p-8 border border-white/10 hover:border-accent/30 transition-all duration-300 h-full">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-gradient-to-br from-accent/20 to-orange-500/20 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <i class="fas fa-analytics text-2xl text-orange-700"></i>
                            </div>
                            <h4 class="text-xl font-bold text-black group-hover:text-accent transition-colors">Application Insights</h4>
                        </div>
                        <p class="text-orange-700 leading-relaxed">Track your application status and get AI-powered insights to improve your success rate dramatically.</p>
                        <div class="flex items-center mt-4 space-x-2">
                            <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style="animation-delay: 2s;"></div>
                            <span class="text-purple-400 text-sm font-medium">Advanced Analytics</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- For Employers Section -->
    <section class="py-10 bg-white relative overflow-hidden">
        <!-- Dynamic Background -->
        <div class="absolute inset-0">
            <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary/10 via-electric/5 to-accent/10"></div>
            <div class="absolute top-1/4 right-0 w-96 h-96 bg-gradient-to-l from-primary/20 to-transparent rounded-full blur-3xl animate-float"></div>
            <div class="absolute bottom-1/4 left-0 w-80 h-80 bg-gradient-to-r from-electric/20 to-transparent rounded-full blur-3xl animate-float" style="animation-delay: 3s;"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-2 gap-16 items-center">
                <div>
                    <div class="mb-8">
                        <span class="text-orange-600 text-sm font-bold tracking-wider uppercase bg-orange-100 px-4 py-2 rounded-full border border-orange-200">
                            🎯 For Employers
                        </span>
                    </div>
                    <h2 class="text-4xl md:text-6xl font-black mb-8 leading-tight">
                        <span class="bg-gradient-to-r from-gray-900 via-blue-700 to-cyan-600 bg-clip-text text-transparent">
                            Hire Top Talent
                        </span>
                        <br>
                        <span class="bg-gradient-to-r from-orange-600 via-orange-500 to-red-500 bg-clip-text text-transparent">
                            Lightning Fast
                        </span>
                    </h2>
                    <p class="text-xl md:text-2xl text-gray-600 mb-10 leading-relaxed">
                        Connect with qualified candidates and build your dream team. Our AI-powered platform makes hiring simple, fast, and effective.
                    </p>

                    <div class="space-y-6 mb-10">
                        <div class="flex items-center group">
                            <div class="w-8 h-8 bg-gradient-to-r from-accent to-orange-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <i class="fas fa-check text-white text-sm"></i>
                            </div>
                            <span class="text-lg text-gray-800 font-medium">Access to 100K+ qualified candidates</span>
                        </div>
                        <div class="flex items-center group">
                            <div class="w-8 h-8 bg-gradient-to-r from-accent to-orange-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <i class="fas fa-check text-white text-sm"></i>
                            </div>
                            <span class="text-lg text-gray-800 font-medium">AI-powered filtering and search tools</span>
                        </div>
                        <div class="flex items-center group">
                            <div class="w-8 h-8 bg-gradient-to-r from-accent to-orange-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <i class="fas fa-check text-white text-sm"></i>
                            </div>
                            <span class="text-lg text-gray-800 font-medium">Dedicated account management</span>
                        </div>
                        <div class="flex items-center group">
                            <div class="w-8 h-8 bg-gradient-to-r from-accent to-orange-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                <i class="fas fa-check text-white text-sm"></i>
                            </div>
                            <span class="text-lg text-gray-800 font-medium">Competitive enterprise pricing</span>
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-6">
                        <a href="jobs.html" class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 rounded-2xl blur-lg group-hover:blur-xl transition-all"></div>
                            <div class="relative glass-morphism border border-white/20 text-black px-8 py-4 rounded-2xl hover:border-white/40 transition-all duration-300 font-semibold text-lg text-center">
                                Browse Talent Pool
                            </div>
                        </a>
                        <a href="#" class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-accent to-orange-500 rounded-2xl blur-lg opacity-50 group-hover:opacity-75 transition-opacity"></div>
                            <div class="relative bg-gradient-to-r from-accent to-orange-500 text-slate-900 px-8 py-4 rounded-2xl font-bold text-lg text-center hover:from-orange-500 hover:to-accent transition-all duration-300">
                                Get Started Now
                            </div>
                        </a>
                    </div>
                </div>

                <div class="text-center">
                    <div class="glass-morphism rounded-3xl p-10 border border-white/20 hover:border-white/30 transition-all duration-300">
                        <div class="grid grid-cols-2 gap-8">
                            <div class="text-center group">
                                <div class="relative mb-4">
                                    <div class="text-4xl md:text-5xl font-black bg-gradient-to-r from-accent to-orange-400 bg-clip-text text-transparent group-hover:scale-110 transition-transform">25K+</div>
                                </div>
                                <div class="text-orange-600 font-semibold">Companies Trust Us</div>
                            </div>
                            <div class="text-center group">
                                <div class="relative mb-4">
                                    <div class="text-4xl md:text-5xl font-black bg-gradient-to-r from-green-400 to-emerald-500 bg-clip-text text-transparent group-hover:scale-110 transition-transform">48hrs</div>
                                </div>
                                <div class="text-green-600 font-semibold">Average Time to Hire</div>
                            </div>
                            <div class="text-center group">
                                <div class="relative mb-4">
                                    <div class="text-4xl md:text-5xl font-black bg-gradient-to-r from-primary to-cyan-400 bg-clip-text text-transparent group-hover:scale-110 transition-transform">95%</div>
                                </div>
                                <div class="text-cyan-600 font-semibold">Employer Satisfaction</div>
                            </div>
                            <div class="text-center group">
                                <div class="relative mb-4">
                                    <div class="text-4xl md:text-5xl font-black bg-gradient-to-r from-electric to-purple-400 bg-clip-text text-transparent group-hover:scale-110 transition-transform">24/7</div>
                                </div>
                                <div class="text-purple-600 font-semibold">AI Support Available</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Success Stories Section -->
    <section class="py-10 bg-gray-50 relative overflow-hidden">
        <!-- Testimonial Background -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-20 w-32 h-32 border border-cyan-300/10 rounded-full animate-float"></div>
            <div class="absolute bottom-20 right-20 w-24 h-24 border border-electric/10 rounded-full animate-float" style="animation-delay: 2s;"></div>
            <div class="absolute top-1/2 left-1/4 w-16 h-16 border border-accent/10 rounded-full animate-float" style="animation-delay: 4s;"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <div class="inline-block mb-6">
                    <span class="text-green-600 text-sm font-bold tracking-wider uppercase bg-green-100 px-4 py-2 rounded-full border border-green-200">
                        ⭐ Success Stories
                    </span>
                </div>
                <h2 class="text-4xl md:text-6xl font-black mb-6">
                    <span class="bg-gradient-to-r from-gray-900 via-green-700 to-emerald-600 bg-clip-text text-transparent">
                        Real People, Real Success
                    </span>
                </h2>
                <p class="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Discover how our platform transformed careers and changed lives
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="group hover-lift">
                    <div class="glass-morphism rounded-3xl p-8 border border-cyan-300/20 hover:border-cyan-300/40 transition-all duration-300 h-full">
                        <div class="flex items-center mb-6">
                            <div class="relative">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face" alt="Sarah Johnson" class="w-16 h-16 rounded-full mr-4 border-2 border-cyan-300/30 group-hover:border-cyan-300/60 transition-all">
                                <div class="absolute inset-0 bg-gradient-to-r from-cyan-300/20 to-blue-400/20 rounded-full blur-lg group-hover:blur-xl transition-all"></div>
                            </div>
                            <div>
                                <h4 class="font-bold text-gray-900 text-lg group-hover:text-cyan-600 transition-colors">Sarah Johnson</h4>
                                <p class="text-gray-600 text-sm font-medium">Software Engineer at Google</p>
                                <div class="flex items-center mt-1">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                                    <span class="text-green-600 text-xs font-medium">Hired in 10 days</span>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-700 mb-6 leading-relaxed italic">"EasyNaukri4U's AI-powered platform helped me land my dream job at Google. The resume builder and interview prep materials were game-changing!"</p>
                        <div class="flex justify-between items-center">
                            <div class="flex text-yellow-400 space-x-1">
                                <i class="fas fa-star text-lg"></i>
                                <i class="fas fa-star text-lg"></i>
                                <i class="fas fa-star text-lg"></i>
                                <i class="fas fa-star text-lg"></i>
                                <i class="fas fa-star text-lg"></i>
                            </div>
                            <span class="text-cyan-600 text-sm font-bold">5.0/5</span>
                        </div>
                    </div>
                </div>

                <div class="group hover-lift">
                    <div class="glass-morphism rounded-3xl p-8 border border-electric/20 hover:border-electric/40 transition-all duration-300 h-full">
                        <div class="flex items-center mb-6">
                            <div class="relative">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face" alt="Michael Chen" class="w-16 h-16 rounded-full mr-4 border-2 border-electric/30 group-hover:border-electric/60 transition-all">
                                <div class="absolute inset-0 bg-gradient-to-r from-electric/20 to-purple-400/20 rounded-full blur-lg group-hover:blur-xl transition-all"></div>
                            </div>
                            <div>
                                <h4 class="font-bold text-gray-900 text-lg group-hover:text-purple-600 transition-colors">Michael Chen</h4>
                                <p class="text-gray-600 text-sm font-medium">Data Scientist at Microsoft</p>
                                <div class="flex items-center mt-1">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                                    <span class="text-blue-600 text-xs font-medium">Hired in 2 weeks</span>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-700 mb-6 leading-relaxed italic">"The AI study materials and virtual mock interviews gave me the confidence I needed. The platform's intelligence is incredible!"</p>
                        <div class="flex justify-between items-center">
                            <div class="flex text-yellow-400 space-x-1">
                                <i class="fas fa-star text-lg"></i>
                                <i class="fas fa-star text-lg"></i>
                                <i class="fas fa-star text-lg"></i>
                                <i class="fas fa-star text-lg"></i>
                                <i class="fas fa-star text-lg"></i>
                            </div>
                            <span class="text-purple-600 text-sm font-bold">5.0/5</span>
                        </div>
                    </div>
                </div>

                <div class="group hover-lift">
                    <div class="glass-morphism rounded-3xl p-8 border border-accent/20 hover:border-accent/40 transition-all duration-300 h-full">
                        <div class="flex items-center mb-6">
                            <div class="relative">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face" alt="Emily Rodriguez" class="w-16 h-16 rounded-full mr-4 border-2 border-accent/30 group-hover:border-accent/60 transition-all">
                                <div class="absolute inset-0 bg-gradient-to-r from-accent/20 to-orange-400/20 rounded-full blur-lg group-hover:blur-xl transition-all"></div>
                            </div>
                            <div>
                                <h4 class="font-bold text-gray-900 text-lg group-hover:text-orange-600 transition-colors">Emily Rodriguez</h4>
                                <p class="text-gray-600 text-sm font-medium">Marketing Manager at Amazon</p>
                                <div class="flex items-center mt-1">
                                    <div class="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
                                    <span class="text-purple-600 text-xs font-medium">Hired in 1 week</span>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-700 mb-6 leading-relaxed italic">"Amazing next-gen platform! The AI job recommendations were spot-on and the application process was seamless. Highly recommended!"</p>
                        <div class="flex justify-between items-center">
                            <div class="flex text-yellow-400 space-x-1">
                                <i class="fas fa-star text-lg"></i>
                                <i class="fas fa-star text-lg"></i>
                                <i class="fas fa-star text-lg"></i>
                                <i class="fas fa-star text-lg"></i>
                                <i class="fas fa-star text-lg"></i>
                            </div>
                            <span class="text-accent text-sm font-bold">5.0/5</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Overall Rating -->
            <div class="mt-16 text-center">
                <div class="glass-dark rounded-2xl p-8 border border-white/10 max-w-2xl mx-auto">
                    <div class="flex items-center justify-center space-x-6">
                        <div class="text-center">
                            <div class="text-4xl font-black bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-2">4.9/5</div>
                            <div class="text-yellow-300 font-semibold">Average Rating</div>
                        </div>
                        <div class="w-px h-16 bg-white/20"></div>
                        <div class="text-center">
                            <div class="text-4xl font-black bg-gradient-to-r from-green-400 to-emerald-500 bg-clip-text text-transparent mb-2">10K+</div>
                            <div class="text-green-300 font-semibold">Success Stories</div>
                        </div>
                        <div class="w-px h-16 bg-white/20"></div>
                        <div class="text-center">
                            <div class="text-4xl font-black bg-gradient-to-r from-blue-400 to-cyan-500 bg-clip-text text-transparent mb-2">98%</div>
                            <div class="text-blue-300 font-semibold">Would Recommend</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-10 bg-white relative overflow-hidden">
        <!-- Dynamic Background -->
        <div class="absolute inset-0">
            <div class="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-primary/10 to-electric/10 rounded-full blur-3xl animate-float"></div>
            <div class="absolute bottom-0 right-1/4 w-80 h-80 bg-gradient-to-r from-accent/10 to-orange-400/10 rounded-full blur-3xl animate-float" style="animation-delay: 3s;"></div>
        </div>

        <div class="relative z-10 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="glass-morphism rounded-3xl p-12 md:p-16 border border-white/20 hover:border-white/30 transition-all duration-300 relative overflow-hidden">
                <!-- Animated Background Pattern -->
                <div class="absolute inset-0 bg-gradient-to-br from-primary/5 via-electric/5 to-accent/5"></div>

                <div class="relative z-10">
                    <div class="mb-8">
                        <span class="text-primary text-sm font-bold tracking-wider uppercase bg-primary/10 px-4 py-2 rounded-full border border-primary/20">
                            📧 Stay Connected
                        </span>
                    </div>

                    <h2 class="text-4xl md:text-6xl font-black mb-6 leading-tight">
                        <span class="bg-gradient-to-r from-black to-blue-300 bg-clip-text text-transparent">
                            Stay Updated
                        </span>
                        <br>
                        <span class="bg-gradient-to-r from-accent via-orange-400 to-red-400 bg-clip-text text-transparent">
                            Never Miss Out
                        </span>
                    </h2>

                    <p class="text-xl md:text-2xl text-cyan-700 mb-12 max-w-3xl mx-auto leading-relaxed">
                        Get the latest job opportunities, AI-powered career tips, and exclusive insights delivered to your inbox
                    </p>

                    <div class="max-w-2xl mx-auto mb-8">
                        <div class="flex flex-col lg:flex-row gap-4">
                            <div class="flex-1 relative group">
                                <div class="absolute inset-0 bg-gradient-to-r from-primary/20 to-electric/20 rounded-2xl blur-lg group-hover:blur-xl transition-all"></div>
                                <input type="email" placeholder="Enter your email address"
                                       class="relative w-full px-6 py-4 bg-slate-800/50 text-white rounded-2xl border border-cyan-300/30 focus:border-cyan-300 focus:ring-2 focus:ring-cyan-300/50 focus:outline-none transition-all placeholder-cyan-200/50 text-lg font-medium">
                            </div>
                            <button class="group relative px-8 py-4 bg-gradient-to-r from-accent to-orange-500 text-slate-900 rounded-2xl font-bold text-lg hover:from-orange-500 hover:to-accent transition-all duration-300 transform hover:scale-105 shimmer-effect">
                                <span class="relative z-10 flex items-center justify-center">
                                    <i class="fas fa-paper-plane mr-3"></i>
                                    Subscribe Now
                                </span>
                                <div class="absolute inset-0 bg-gradient-to-r from-accent to-orange-500 rounded-2xl blur-lg opacity-50 group-hover:opacity-75 transition-opacity"></div>
                            </button>
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="grid md:grid-cols-3 gap-6 mb-8">
                        <div class="flex items-center justify-center space-x-3 glass-dark px-4 py-3 rounded-full border border-white/10">
                            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <span class="text-green-300 font-medium text-sm">Weekly Job Alerts</span>
                        </div>
                        <div class="flex items-center justify-center space-x-3 glass-dark px-4 py-3 rounded-full border border-white/10">
                            <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
                            <span class="text-blue-300 font-medium text-sm">AI Career Tips</span>
                        </div>
                        <div class="flex items-center justify-center space-x-3 glass-dark px-4 py-3 rounded-full border border-white/10">
                            <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style="animation-delay: 2s;"></div>
                            <span class="text-purple-300 font-medium text-sm">Exclusive Insights</span>
                        </div>
                    </div>

                    <p class="text-sm text-cyan-400 font-medium">
                        <i class="fas fa-shield-alt mr-2 text-green-600"></i>
                        No spam, unsubscribe at any time. Your privacy is protected.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="hero-bg pt-16 pb-2 relative overflow-hidden">
        <div class="absolute inset-0">
            <div class="absolute top-20 left-10 w-2 h-2 bg-black-400 rounded-full animate-pulse"></div>
            <div class="absolute top-40 right-20 w-1 h-1 bg-black rounded-full animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute bottom-40 left-1/4 w-1.5 h-1.5 bg-black rounded-full animate-pulse" style="animation-delay: 2s;"></div>
            <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-black rounded-full animate-pulse" style="animation-delay: 3s;"></div>
        </div>
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-12">
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="relative">
                            <div class="bg-gradient-to-br from-primary to-electric p-3 rounded-xl">
                                <i class="fas fa-briefcase text-2xl text-white"></i>
                            </div>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-2xl font-black bg-gradient-to-r from-black to-cyan-700 bg-clip-text text-transparent">USA.EasyNaukri4U</span>
                            <span class="text-xs text-cyan-700 font-medium tracking-wider">NEXT-GEN CAREERS</span>
                        </div>
                    </div>
                    <p class="text-blue-700 mb-2 leading-relaxed max-w-md">Your trusted AI-powered partner in finding the perfect job opportunities across the United States. The future of career advancement starts here.</p>

                    <!-- Social Links -->
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="group">
                            <div class="w-12 h-12 glass-morphism rounded-xl flex items-center justify-center border border-white/10 group-hover:border-cyan-300/50 transition-all duration-300">
                                <i class="fab fa-facebook text-cyan-700 group-hover:text-white transition-colors"></i>
                            </div>
                        </a>
                        <a href="https://www.twitter.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="group">
                            <div class="w-12 h-12 glass-morphism rounded-xl flex items-center justify-center border border-white/10 group-hover:border-electric/50 transition-all duration-300">
                                <i class="fab fa-twitter text-electric group-hover:text-white transition-colors"></i>
                            </div>
                        </a>
                        <a href="https://www.linkedin.com/company/easynaukri4u" target="_blank" rel="noopener noreferrer" class="group">
                            <div class="w-12 h-12 glass-morphism rounded-xl flex items-center justify-center border border-white/10 group-hover:border-blue-400/50 transition-all duration-300">
                                <i class="fab fa-linkedin text-blue-700 group-hover:text-white transition-colors"></i>
                            </div>
                        </a>
                        <a href="https://www.instagram.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="group">
                            <div class="w-12 h-12 glass-morphism rounded-xl flex items-center justify-center border border-white/10 group-hover:border-accent/50 transition-all duration-300">
                                <i class="fab fa-instagram text-accent group-hover:text-white transition-colors"></i>
                            </div>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="font-black text-black mb-6 text-lg">For Job Seekers</h3>
                    <ul class="space-y-4">
                        <li><a href="jobs.html" class="group flex items-center text-cyan-700 hover:text-cyan-900 transition-colors">
                            <i class="fas fa-search mr-3 text-cyan-700 group-hover:text-cyan-900 transition-colors"></i>
                            Browse Jobs
                        </a></li>
                        <li><a href="resume-builder.html" class="group flex items-center text-cyan-700 hover:text-cyan-900 transition-colors">
                            <i class="fas fa-file-alt mr-3 text-cyan-700 group-hover:text-cyan-900 transition-colors"></i>
                            AI Resume Builder
                        </a></li>
                        <li><a href="study-material.html" class="group flex items-center text-cyan-700 hover:text-cyan-900 transition-colors">
                            <i class="fas fa-graduation-cap mr-3 text-cyan-700 group-hover:text-cyan-900 transition-colors"></i>
                            Study Materials
                        </a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-black text-black mb-6 text-lg">Support & Legal</h3>
                    <ul class="space-y-4">
                        <li><a href="help-center.html" class="group flex items-center text-purple-700 hover:text-purple-900 transition-colors">
                            <i class="fas fa-question-circle mr-3 text-purple-700 group-hover:text-purple-900 transition-colors"></i>
                            Help Center
                        </a></li>
                        <li><a href="privacy-policy.html" class="group flex items-center text-purple-700 hover:text-purple-900 transition-colors">
                            <i class="fas fa-shield-alt mr-3 text-purple-700 group-hover:text-purple-900 transition-colors"></i>
                            Privacy Policy
                        </a></li>
                        <li><a href="terms-of-service.html" class="group flex items-center text-purple-700 hover:text-purple-900 transition-colors">
                            <i class="fas fa-file-contract mr-3 text-purple-700 group-hover:text-purple-900 transition-colors"></i>
                            Terms of Service
                        </a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Section -->
            <div class="border-t border-black/50 mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-center md:text-left md:mb-0">
                        <p class="text-cyan-700 font-medium">&copy; 2025 USA.EasyNaukri4U. All rights reserved.</p>
                        <p class="text-cyan-700/60 text-sm mt-1">Powered by Advanced AI Technology</p>
                    </div>
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-700 rounded-full animate-pulse"></div>
                            <span class="text-green-700 text-sm font-medium">99.9% Uptime</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-blue-700 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
                            <span class="text-blue-700 text-sm font-medium">SSL Secured</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-purple-700 rounded-full animate-pulse" style="animation-delay: 2s;"></div>
                            <span class="text-purple-700 text-sm font-medium">GDPR Compliant</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script>
        // Initialize search functionality on homepage
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof EasyNaukri !== 'undefined' && EasyNaukri.initializeSearch) {
                EasyNaukri.initializeSearch();
            }
        });
    </script>

    <!-- Jobs Loading Script -->
    <script type="module">
        // Import Firebase configuration from the same file as jobs.html
        import { firebaseJobs } from './js/firebase-config.js';

        // Wait for DOM to be loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing homepage jobs...');
            setTimeout(loadFeaturedJobs, 500); // Small delay to ensure everything is ready
        });

        async function loadFeaturedJobs() {
            try {
                console.log('Loading featured jobs from Firebase...');

                // Use the same method as jobs.html
                const jobs = await firebaseJobs.getJobs({
                    status: null, // Get all jobs regardless of status
                    limitCount: 3 // Limit to 3 jobs for homepage
                });

                console.log(`Loaded ${jobs.length} jobs from Firebase`);
                console.log('Jobs data:', jobs);

                displayJobs(jobs);

            } catch (error) {
                console.error('Error loading jobs:', error);
                console.error('Error details:', error.message);
                showNoJobsMessage();
            }
        }

        function displayJobs(jobs) {
            const jobsContainer = document.getElementById('jobs-container');

            if (jobs.length === 0) {
                showNoJobsMessage();
                return;
            }

            // Clear loading placeholder
            jobsContainer.innerHTML = '';

            jobs.forEach((job, index) => {
                const jobCard = createJobCard(job, index);
                jobsContainer.appendChild(jobCard);
            });
        }

        function createJobCard(job, index) {
            const div = document.createElement('div');
            div.className = 'bg-white rounded-lg shadow-md hover:shadow-lg transition p-6 cursor-pointer h-full flex flex-col';

            // Add click handler to redirect to jobs page
            div.addEventListener('click', function() {
                console.log('Job card clicked, redirecting to jobs page');
                window.location.href = 'jobs.html';
            });

            // Get company icon based on company name
            const companyIcon = getCompanyIcon(job.company);
            const companyColor = getCompanyColor(index);

            // Format salary using jobs page logic
            const salaryInfo = formatSalaryDisplay(job);

            // Format date
            const postedDate = formatDate(job.createdAt);

            div.innerHTML = `
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 ${companyColor.bg} rounded-lg flex items-center justify-center">
                            <i class="${companyIcon} ${companyColor.text} text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900">${job.title || 'Job Title'}</h3>
                            <p class="text-gray-600">${job.company || 'Company Name'}</p>
                        </div>
                    </div>
                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">${job.type || 'Full-time'}</span>
                </div>
                <div class="space-y-2 mb-4">
                    <div class="flex items-center text-gray-600">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        <span>${job.location || 'Location not specified'}</span>
                    </div>
                    <div class="flex items-center text-gray-600">
                        <i class="fas fa-dollar-sign mr-2"></i>
                        <span>${salaryInfo.shortAmount}</span>
                    </div>
                    <div class="flex items-center text-gray-600">
                        <i class="fas fa-clock mr-2"></i>
                        <span>Posted ${postedDate}</span>
                    </div>
                </div>
                <div class="flex-grow">
                    ${job.description ? `<p class="text-gray-700 mb-4 text-sm">${String(job.description).substring(0, 120)}${String(job.description).length > 120 ? '...' : ''}</p>` : ''}
                    <div class="flex flex-wrap gap-2 mb-4">
                        ${getSkillsHTML(job.skills)}
                    </div>
                </div>
                <div class="mt-auto pt-4 border-t border-gray-200">
                    <div class="text-center">
                        <span class="text-primary font-medium text-sm">Click to view all jobs →</span>
                    </div>
                </div>
            `;

            return div;
        }

        function getSkillsHTML(skills) {
            if (!skills) return '';

            let skillsArray = [];

            // Handle different skill formats
            if (Array.isArray(skills)) {
                // Skills is already an array
                skillsArray = skills;
            } else if (typeof skills === 'string') {
                // Skills is a string, split by comma
                skillsArray = skills.split(',');
            } else {
                // Unknown format, return empty
                return '';
            }

            // Take first 3 skills and create HTML
            return skillsArray
                .slice(0, 3)
                .map(skill => {
                    const trimmedSkill = typeof skill === 'string' ? skill.trim() : String(skill).trim();
                    return trimmedSkill ? `<span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">${trimmedSkill}</span>` : '';
                })
                .filter(html => html !== '')
                .join('');
        }

        function getCompanyIcon(companyName) {
            if (!companyName) return 'fas fa-building';
            const company = companyName.toLowerCase();
            if (company.includes('google')) return 'fab fa-google';
            if (company.includes('microsoft')) return 'fab fa-microsoft';
            if (company.includes('amazon')) return 'fab fa-amazon';
            if (company.includes('apple')) return 'fab fa-apple';
            if (company.includes('facebook') || company.includes('meta')) return 'fab fa-facebook';
            if (company.includes('netflix')) return 'fas fa-film';
            if (company.includes('uber')) return 'fab fa-uber';
            if (company.includes('airbnb')) return 'fab fa-airbnb';
            return 'fas fa-building';
        }

        function getCompanyColor(index) {
            const colors = [
                { bg: 'bg-blue-100', text: 'text-blue-600' },
                { bg: 'bg-green-100', text: 'text-green-600' },
                { bg: 'bg-purple-100', text: 'text-purple-600' },
                { bg: 'bg-red-100', text: 'text-red-600' },
                { bg: 'bg-yellow-100', text: 'text-yellow-600' },
                { bg: 'bg-indigo-100', text: 'text-indigo-600' }
            ];
            return colors[index % colors.length];
        }

        function formatSalaryDisplay(job) {
            // Match jobs page formatting by echoing provided salary text with optional period
            let salaryText = '';
            if (job.salary === null || job.salary === undefined) {
                salaryText = 'Not specified';
            } else {
                salaryText = String(job.salary);
            }

            if (!salaryText || salaryText.trim() === '' || salaryText === 'Not specified') {
                return {
                    amount: 'Not specified',
                    period: '',
                    shortAmount: 'N/A'
                };
            }

            // Determine period text similar to jobs page
            let periodText = '';
            if (job.salaryPeriod) {
                switch (job.salaryPeriod) {
                    case 'monthly':
                        periodText = 'per month';
                        break;
                    case 'hourly':
                        periodText = 'per hour';
                        break;
                    case 'yearly':
                        periodText = 'per year';
                        break;
                    default:
                        periodText = 'per year';
                        break;
                }
            }

            // Add $ sign if not present and looks numeric/range
            let displaySalary = salaryText;
            if (!displaySalary.includes('$') && (displaySalary.match(/^\d/) || displaySalary.includes('-'))) {
                displaySalary = '$' + displaySalary;
            }

            return {
                amount: displaySalary,
                period: periodText,
                shortAmount: displaySalary
            };
        }

        function formatDate(timestamp) {
            if (!timestamp) return 'Recently';

            let date;
            if (timestamp.toDate) {
                // Firestore timestamp
                date = timestamp.toDate();
            } else if (timestamp instanceof Date) {
                date = timestamp;
            } else {
                // Try to parse as date
                date = new Date(timestamp);
            }

            const now = new Date();
            const diffTime = Math.abs(now - date);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays === 1) return '1 day ago';
            if (diffDays < 7) return `${diffDays} days ago`;
            if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
            return `${Math.ceil(diffDays / 30)} months ago`;
        }

        function showNoJobsMessage() {
            const jobsContainer = document.getElementById('jobs-container');
            jobsContainer.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <i class="fas fa-briefcase text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No Jobs Available</h3>
                    <p class="text-gray-600 mb-6">We're currently updating our job listings. Please check back soon!</p>
                    <div class="space-y-3">
                        <a href="jobs.html" class="inline-flex items-center bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition">
                            Browse All Jobs <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                        <br>
                        <button onclick="loadFeaturedJobs()" class="inline-flex items-center bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition text-sm">
                            <i class="fas fa-sync-alt mr-2"></i>Retry Loading
                        </button>
                    </div>
                </div>
            `;
        }

        // Debug function to check Firebase connection
        window.debugHomepageJobs = async function() {
            console.log('=== HOMEPAGE JOBS DEBUG ===');
            try {
                const { firebaseJobs } = await import('./js/firebase-config.js');
                const jobs = await firebaseJobs.getJobs({ status: null });
                console.log('Total jobs in database:', jobs.length);
                console.log('Jobs:', jobs);
            } catch (error) {
                console.error('Debug error:', error);
            }
        };

        // Make loadFeaturedJobs globally accessible for retry button
        window.loadFeaturedJobs = loadFeaturedJobs;
    </script>
    </script>
</body>
</html>
