<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Find Jobs - USA EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            },
            // Ensure classes used in dynamic templates are preserved on CDN builds
            safelist: [
                // layout and spacing
                'flex','flex-col','flex-wrap','items-center','items-start','justify-between','gap-1','gap-2','gap-4','space-x-4',
                'min-w-0','shrink-0','truncate','overflow-hidden',
                // visibility
                'hidden','md:hidden','md:flex',
                // text sizes used around info/salary
                'text-xs','text-sm','text-base','text-lg','xl:text-base','2xl:text-lg','xl:text-xl','2xl:text-2xl',
                // colors and font weights
                'text-green-600','text-gray-500','text-gray-600','text-gray-900','font-bold','font-medium',
                // borders and utilities
                'border','border-gray-100','border-gray-300','rounded-lg','whitespace-nowrap',
                // grid helpers (if grid view used)
                'grid','grid-cols-1','sm:grid-cols-2','md:grid-cols-3','lg:grid-cols-4','gap-4'
            ]
        }
    </script>
    <!-- adsense -->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>
    <style>
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>

</head>
<body class="font-inter bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition">Home</a>
                    <a href="jobs.html" class="text-primary font-medium">Find Jobs</a>
                    <a href="study-material.html" class="text-gray-700 hover:text-primary transition">Study Materials</a>
                    <a href="resume-builder.html" class="text-gray-700 hover:text-primary transition">Resume Builder</a>
                    <!-- For Employers dropdown removed for security -->
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="index.html" class="block py-2 text-primary font-medium">Home</a>
                <a href="jobs.html" class="block py-2 text-gray-700">Find Jobs</a>
                <a href="study-material.html" class="block py-2 text-gray-700">Study Materials</a>
                <a href="resume-builder.html" class="block py-2 text-gray-700">Resume Builder</a>
            </div>
        </div>
    </nav>

    <!-- Search Header -->
    <section class="bg-white py-6 border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row gap-4">
                <!-- Search Bar -->
                <div class="flex-1 bg-gray-50 rounded-lg p-4">
                    <div class="flex flex-col md:flex-row gap-3">
                        <div class="flex-1">
                            <div class="relative">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                <input type="text" id="job-search-input" placeholder="Job title, keywords, or company"
                                       class="search-input w-full pl-10 pr-4 py-3 text-gray-900 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                        </div>
                        <div class="flex-1">
                            <div class="relative">
                                <i class="fas fa-map-marker-alt absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                <input type="text" id="location-search-input" placeholder="City, state, or zip code"
                                       class="search-input w-full pl-10 pr-4 py-3 text-gray-900 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                        </div>
                        <button id="search-btn" class="search-btn bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition font-medium whitespace-nowrap">
                            <i class="fas fa-search mr-2"></i>Search
                        </button>
                    </div>
                </div>

                <!-- Sort Options -->
                <div class="lg:w-48">
                    <select id="sort-select" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="relevance">Sort by: Relevance</option>
                        <option value="date">Sort by: Date Posted</option>
                        <option value="salary-high">Sort by: Salary (High to Low)</option>
                        <option value="salary-low">Sort by: Salary (Low to High)</option>
                        <option value="company">Sort by: Company Name</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Filters Sidebar -->
            <div class="lg:w-80">
                <div class="bg-white rounded-lg shadow-md p-6 sticky top-24">
                    <h3 class="font-semibold text-gray-900 mb-4">Filters</h3>
                    
                    <!-- Job Category -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Job Category</h4>
                        <div class="space-y-2" id="category-filters">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="category" value="software-development" class="rounded border-gray-300 text-primary focus:ring-primary">
                                <span>Software Development</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="category" value="data-science" class="rounded border-gray-300 text-primary focus:ring-primary">
                                <span>Data Science</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="category" value="design" class="rounded border-gray-300 text-primary focus:ring-primary">
                                <span>Design</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="category" value="marketing" class="rounded border-gray-300 text-primary focus:ring-primary">
                                <span>Marketing</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="category" value="sales" class="rounded border-gray-300 text-primary focus:ring-primary">
                                <span>Sales</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="category" value="hr" class="rounded border-gray-300 text-primary focus:ring-primary">
                                <span>Human Resources</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="category" value="finance" class="rounded border-gray-300 text-primary focus:ring-primary">
                                <span>Finance</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="category" value="operations" class="rounded border-gray-300 text-primary focus:ring-primary">
                                <span>Operations</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" name="category" value="other" class="rounded border-gray-300 text-primary focus:ring-primary">
                                <span>Other</span>
                            </label>
                        </div>
                    </div>

                    <!-- Job Type -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Job Type</h4>
                        <div class="space-y-2" id="job-type-filters">
                            <!-- Dynamic job type filters will be inserted here -->
                        </div>
                    </div>

                    <!-- Salary Range -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Salary Range</h4>
                        <div class="space-y-2" id="salary-filters">
                            <!-- Dynamic salary filters will be inserted here -->
                        </div>
                    </div>

                    <!-- Experience Level -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Experience Level</h4>
                        <div class="space-y-2" id="experience-filters">
                            <!-- Dynamic experience filters will be inserted here -->
                        </div>
                    </div>

                    <!-- Location -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Location</h4>
                        <div class="space-y-2" id="location-filters">
                            <!-- Dynamic location filters will be inserted here -->
                        </div>
                    </div>

                    <!-- Clear Filters -->
                    <button class="clear-filters-btn w-full bg-gray-100 text-gray-700 py-2 rounded-lg hover:bg-gray-200 transition">
                        Clear All Filters
                    </button>
                </div>
            </div>

            <!-- Job Listings -->
            <div class="flex-1">
                <!-- Results Header -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Job Results</h2>
                        <p id="job-count" class="text-gray-600 mt-1">Loading jobs...</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="grid-view-btn" class="p-2 text-gray-400 hover:text-primary transition" title="Grid View">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button id="list-view-btn" class="p-2 text-primary" title="List View">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <!-- Job Cards Container (Dynamic) -->
                <div class="space-y-4" id="jobs-container">
                    <!-- Jobs will be rendered here dynamically -->
                </div>



                <!-- Pagination -->
                <div class="flex justify-center mt-8" id="pagination-container">
                    <nav class="flex items-center space-x-2" id="pagination-nav">
                        <!-- Pagination will be generated dynamically -->
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold">USA.EasyNaukri4U</span>
                    </div>
                    <p class="text-gray-400 mb-4">Your trusted partner in finding the perfect job opportunities across the United States.</p>
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.twitter.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="https://www.linkedin.com/company/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Connect with us on LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.instagram.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Job Seekers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="jobs.html" class="hover:text-white transition">Browse Jobs</a></li>
                        <!-- <li><a href="login.html" class="hover:text-white transition">Sign In</a></li> -->
                        <li><a href="resume-builder.html" class="hover:text-white transition">Resume Builder</a></li>
                        <li><a href="study-material.html" class="hover:text-white transition">Study Materials</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Employers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition">Pricing</a></li>
                        <li><a href="#" class="hover:text-white transition">Resources</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="help-center.html" class="hover:text-white transition">Help Center</a></li>
                        <!-- <li><a href="help-center.html" class="hover:text-white transition">Contact Us</a></li> -->
                        <li><a href="privacy-policy.html" class="hover:text-white transition">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html" class="hover:text-white transition">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 USA.EasyNaukri4U. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script type="module">
        // Import Firebase modules
        import { firebaseJobs, FirebaseUtils } from './js/firebase-config.js';

        // EasyNaukri namespace for utility functions
        window.EasyNaukri = {
            // Show notification
            showNotification: function(message, type = 'info') {
                FirebaseUtils.showNotification(message, type);
            }
        };

        // Global variables for job management
        let allJobs = [];
        let filteredJobs = [];
        let currentPage = 1;
        let jobsPerPage = 6; // Show 6 jobs per page
        let currentView = 'list';
        let isLoading = false;

        // Load jobs from Firebase Firestore
        async function loadJobsFromFirebase() {
            if (isLoading) return;
            
            isLoading = true;
            showLoadingState();

            try {
                console.log('Loading jobs from Firebase...');
                
                // First try to get active jobs
                let jobs = await firebaseJobs.getJobs({
                    status: 'active',
                    limitCount: 50
                });

                // If no active jobs, try getting any jobs
                if (jobs.length === 0) {
                    console.log('No active jobs found, trying all jobs...');
                    jobs = await firebaseJobs.getJobs({
                        status: null,
                        limitCount: 50
                    });
                }

                console.log(`Loaded ${jobs.length} jobs from Firebase`);
                console.log('All jobs raw data:', jobs);

                // Transform Firebase data to match expected format
                allJobs = jobs.map(job => {
                    // Debug log to see what data we're getting from Firebase
                    console.log('Raw job data from Firebase:', job);

                    return {
                        id: job.id,
                        title: job.title,
                        company: job.company,
                        location: job.location,
                        salary: job.salary || 'Not specified', // Keep salary as text - exactly as entered
                        salaryPeriod: job.salaryPeriod || 'yearly',
                        salaryRange: job.salary ? getSalaryRange(job.salary) : 'Not specified',
                        posted: job.createdAt,
                        applicants: job.applications || 0,
                        description: job.description,
                        skills: job.skills || [],
                        type: job.type || 'Full-time',
                        experience: job.experience || 'Mid',
                        category: job.category,
                        workLocation: job.workLocation,
                        applicationEmail: job.applicationEmail,
                        applicationUrl: job.applicationUrl || job.careerPageUrl || job.companyUrl,
                        views: job.views || 0,
                        postedBy: job.postedBy || 'system'
                    };
                });

                // Initialize filtered jobs
                filteredJobs = [...allJobs];

                // Generate dynamic filters
                generateDynamicFilters();

                // Initial render
                renderJobs();
                updateJobCount();
                updatePagination();

                hideLoadingState();
                
                if (allJobs.length === 0) {
                    // Show fallback jobs instead of empty state
                    loadFallbackJobs();
                } else {
                    EasyNaukri.showNotification(`Loaded ${allJobs.length} job(s) successfully`, 'success');
                }

            } catch (error) {
                console.error('Error loading jobs from Firebase:', error);
                hideLoadingState();

                // Show fallback jobs instead of error state
                loadFallbackJobs();
                EasyNaukri.showNotification('Showing sample jobs. Click retry to load live jobs.', 'info');
            } finally {
                isLoading = false;
            }
        }

        // Helper function to determine salary range
        function getSalaryRange(salary) {
            if (!salary || salary === 'Not specified') return 'Not specified';
            
            // If salary is already text (like range), return as is
            if (typeof salary === 'string' && isNaN(salary)) {
                return salary;
            }
            
            // If salary is numeric, categorize it
            const numericSalary = parseInt(salary);
            if (isNaN(numericSalary) || numericSalary === 0) return 'Not specified';

            if (numericSalary >= 100000) return '100k+';
            if (numericSalary >= 75000) return '75k-100k';
            if (numericSalary >= 50000) return '50k-75k';
            return '30k-50k';
        }

        // Helper function to format salary display - Simple text display
        function formatSalaryDisplay(job) {
            console.log('=== formatSalaryDisplay called ===');
            console.log('Job:', job.title, 'Salary:', job.salary, 'Type:', typeof job.salary);

            // Convert salary to string - handle both string and number types
            let salaryText = '';
            if (job.salary === null || job.salary === undefined) {
                salaryText = 'Not specified';
            } else {
                salaryText = String(job.salary);
            }
            
            // If salary is empty or null, show default
            if (!salaryText || salaryText.trim() === '' || salaryText === 'Not specified') {
                return {
                    amount: 'Not specified',
                    period: '',
                    shortAmount: 'N/A'
                };
            }

            // Determine period text
            let periodText = '';
            if (job.salaryPeriod) {
                switch (job.salaryPeriod) {
                    case 'monthly':
                        periodText = 'per month';
                        break;
                    case 'hourly':
                        periodText = 'per hour';
                        break;
                    case 'yearly':
                        periodText = 'per year';
                        break;
                    default:
                        periodText = 'per year';
                        break;
                }
            }

            // Add $ sign if not already present and if it looks like a number/range
            let displaySalary = salaryText;
            if (!displaySalary.includes('$') && (displaySalary.match(/^\d/) || displaySalary.includes('-'))) {
                displaySalary = '$' + displaySalary;
            }

            const result = {
                amount: displaySalary,
                period: periodText,
                shortAmount: displaySalary // Same for both full and short display
            };

            console.log('Final salary result:', result);
            return result;
        }

        // Show loading state
        function showLoadingState() {
            const jobsContainer = document.getElementById('jobs-container');
            if (jobsContainer) {
                jobsContainer.innerHTML = `
                    <div class="text-center py-12">
                        <i class="fas fa-spinner fa-spin text-primary text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Loading Jobs...</h3>
                        <p class="text-gray-600">Please wait while we fetch the latest job openings</p>
                    </div>
                `;
            }
        }

        // Hide loading state
        function hideLoadingState() {
            // Loading state will be replaced by renderJobs()
        }

        // Show empty state
        function showEmptyState() {
            const jobsContainer = document.getElementById('jobs-container');
            if (jobsContainer) {
                jobsContainer.innerHTML = `
                    <div class="text-center py-12">
                        <i class="fas fa-briefcase text-gray-300 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Jobs Available</h3>
                        <p class="text-gray-600 mb-4">There are currently no job openings posted. Please check back later.</p>
                        <button onclick="loadJobsFromFirebase()" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-secondary transition">
                            <i class="fas fa-sync-alt mr-2"></i>Refresh Jobs
                        </button>
                    </div>
                `;
            }
        }

        // Show error state
        function showErrorState() {
            const jobsContainer = document.getElementById('jobs-container');
            if (jobsContainer) {
                jobsContainer.innerHTML = `
                    <div class="text-center py-12">
                        <i class="fas fa-exclamation-triangle text-red-400 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Jobs</h3>
                        <p class="text-gray-600 mb-4">We couldn't load the job listings. Please try again.</p>
                        <button onclick="loadJobsFromFirebase()" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-secondary transition">
                            <i class="fas fa-sync-alt mr-2"></i>Try Again
                        </button>
                    </div>
                `;
            }
        }

        // Initialize page when DOM is loaded
        // Debug function to manually check Firebase data
        window.debugFirebaseData = async function() {
            try {
                console.log('=== MANUAL FIREBASE DEBUG ===');
                const jobs = await firebaseJobs.getJobs({ status: null });
                console.log('Raw Firebase jobs:', jobs);

                jobs.forEach((job, index) => {
                    console.log(`Job ${index + 1}:`, {
                        title: job.title,
                        salary: job.salary,
                        salaryMax: job.salaryMax,
                        salaryMin: job.salaryMin,
                        salaryPeriod: job.salaryPeriod,
                        allData: job
                    });
                });
            } catch (error) {
                console.error('Debug error:', error);
            }
        };

        document.addEventListener('DOMContentLoaded', async function() {
            console.log('DOM loaded, initializing Firebase jobs page...');

            try {
                // Initialize dynamic job system
                initializeDynamicJobs();

                // Initialize sort functionality
                initializeSortBy();

                // Initialize view toggle functionality
                initializeViewToggle();

                // Load jobs from Firebase
                await loadJobsFromFirebase();

                // Auto-run debug after 2 seconds
                setTimeout(() => {
                    console.log('Auto-running Firebase debug...');
                    window.debugFirebaseData();
                }, 2000);

                // Initialize main functionality after jobs are loaded
                if (typeof EasyNaukri !== 'undefined' && EasyNaukri.initializeSearch) {
                    EasyNaukri.initializeSearch();
                }
                if (typeof EasyNaukri !== 'undefined' && EasyNaukri.initializeBookmarks) {
                    EasyNaukri.initializeBookmarks();
                }
                // Note: We don't call EasyNaukri.initializeCheckboxFilters() here because
                // this page has its own filter system that works with Firebase data

                console.log('Firebase jobs page initialized successfully');

            } catch (error) {
                console.error('Error initializing jobs page:', error);
                EasyNaukri.showNotification('Error initializing page. Please refresh.', 'error');
            }
        });

        // Load fallback jobs when Firebase fails
        function loadFallbackJobs() {
            console.log('Loading fallback jobs...');

            // Create comprehensive sample jobs
            const fallbackJobs = [
                {
                    id: 'fallback-1',
                    title: 'Senior Software Engineer',
                    company: 'TechCorp Solutions',
                    location: 'San Francisco, CA',
                    salary: '$120,000 - $160,000',
                    salaryPeriod: 'yearly',
                    salaryRange: '$120,000 - $160,000',
                    posted: new Date(),
                    applicants: 15,
                    description: 'Join our dynamic team and work on cutting-edge technology projects. We are looking for experienced developers to help build scalable web applications.',
                    skills: ['JavaScript', 'React', 'Node.js', 'AWS', 'Docker'],
                    type: 'Full-time',
                    experience: 'Senior',
                    category: 'Technology',
                    workLocation: 'Hybrid',
                    applicationEmail: '<EMAIL>',
                    applicationUrl: 'https://techcorp.com/careers',
                    views: 245,
                    postedBy: 'HR Team'
                },
                {
                    id: 'fallback-2',
                    title: 'Marketing Manager',
                    company: 'Growth Dynamics Inc',
                    location: 'New York, NY',
                    salary: '$80,000 - $100,000',
                    salaryPeriod: 'yearly',
                    salaryRange: '$80,000 - $100,000',
                    posted: new Date(Date.now() - 86400000), // 1 day ago
                    applicants: 28,
                    description: 'Lead marketing campaigns and drive business growth. Develop comprehensive marketing strategies and manage cross-functional teams.',
                    skills: ['Marketing Strategy', 'Digital Marketing', 'Analytics', 'SEO', 'Content Marketing'],
                    type: 'Full-time',
                    experience: 'Mid',
                    category: 'Marketing',
                    workLocation: 'Remote',
                    applicationEmail: '<EMAIL>',
                    applicationUrl: 'https://growthdynamics.com/jobs',
                    views: 189,
                    postedBy: 'Marketing Team'
                },
                {
                    id: 'fallback-3',
                    title: 'Data Analyst',
                    company: 'Analytics Pro',
                    location: 'Austin, TX',
                    salary: '$70,000 - $90,000',
                    salaryPeriod: 'yearly',
                    salaryRange: '$70,000 - $90,000',
                    posted: new Date(Date.now() - *********), // 2 days ago
                    applicants: 42,
                    description: 'Analyze complex datasets to provide actionable insights. Work with stakeholders to identify trends and drive data-driven decision making.',
                    skills: ['Python', 'SQL', 'Tableau', 'Excel', 'Statistics'],
                    type: 'Full-time',
                    experience: 'Mid',
                    category: 'Data Science',
                    workLocation: 'On-site',
                    applicationEmail: '<EMAIL>',
                    applicationUrl: 'https://analyticspro.com/careers',
                    views: 156,
                    postedBy: 'Data Team'
                },
                {
                    id: 'fallback-4',
                    title: 'UX/UI Designer',
                    company: 'Design Studio',
                    location: 'Los Angeles, CA',
                    salary: '$75,000 - $95,000',
                    salaryPeriod: 'yearly',
                    salaryRange: '$75,000 - $95,000',
                    posted: new Date(Date.now() - *********), // 3 days ago
                    applicants: 33,
                    description: 'Create intuitive and beautiful user experiences. Collaborate with product teams to design user-centered digital products.',
                    skills: ['Figma', 'Adobe Creative Suite', 'Prototyping', 'User Research', 'Wireframing'],
                    type: 'Full-time',
                    experience: 'Mid',
                    category: 'Design',
                    workLocation: 'Hybrid',
                    applicationEmail: '<EMAIL>',
                    applicationUrl: 'https://designstudio.com/jobs',
                    views: 201,
                    postedBy: 'Design Team'
                },
                {
                    id: 'fallback-5',
                    title: 'Project Manager',
                    company: 'Enterprise Solutions',
                    location: 'Chicago, IL',
                    salary: '$85,000 - $110,000',
                    salaryPeriod: 'yearly',
                    salaryRange: '$85,000 - $110,000',
                    posted: new Date(Date.now() - *********), // 4 days ago
                    applicants: 19,
                    description: 'Lead cross-functional teams to deliver projects on time and within budget. Manage project lifecycle from initiation to closure.',
                    skills: ['Project Management', 'Agile', 'Scrum', 'Leadership', 'Communication'],
                    type: 'Full-time',
                    experience: 'Senior',
                    category: 'Management',
                    workLocation: 'On-site',
                    applicationEmail: '<EMAIL>',
                    applicationUrl: 'https://enterprisesolutions.com/careers',
                    views: 134,
                    postedBy: 'PMO'
                },
                {
                    id: 'fallback-6',
                    title: 'Frontend Developer',
                    company: 'WebTech Innovations',
                    location: 'Seattle, WA',
                    salary: '$90,000 - $120,000',
                    salaryPeriod: 'yearly',
                    salaryRange: '$90,000 - $120,000',
                    posted: new Date(Date.now() - *********), // 5 days ago
                    applicants: 51,
                    description: 'Build responsive and interactive web applications. Work with modern frameworks and collaborate with design teams.',
                    skills: ['React', 'TypeScript', 'CSS', 'HTML', 'Git'],
                    type: 'Full-time',
                    experience: 'Mid',
                    category: 'Technology',
                    workLocation: 'Remote',
                    applicationEmail: '<EMAIL>',
                    applicationUrl: 'https://webtech.com/jobs',
                    views: 298,
                    postedBy: 'Engineering Team'
                }
            ];

            // Set fallback jobs as current jobs
            allJobs = fallbackJobs;
            filteredJobs = [...allJobs];

            // Generate dynamic filters
            generateDynamicFilters();

            // Render jobs
            renderJobs();
            updateJobCount();
            updatePagination();

            // Show notice about fallback data
            setTimeout(() => {
                const jobsContainer = document.getElementById('jobs-container');
                if (jobsContainer) {
                    const notice = document.createElement('div');
                    notice.className = 'col-span-full mb-6';
                    notice.innerHTML = `
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2 text-blue-700">
                                    <i class="fas fa-info-circle"></i>
                                    <span class="text-sm font-medium">Showing sample jobs. <button onclick="loadJobsFromFirebase()" class="underline hover:no-underline font-semibold">Click here to retry loading live jobs</button></span>
                                </div>
                                <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-blue-500 hover:text-blue-700">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    `;
                    jobsContainer.insertBefore(notice, jobsContainer.firstChild);
                }
            }, 100);
        }

        // Make functions globally accessible
        window.loadJobsFromFirebase = loadJobsFromFirebase;
        window.loadFallbackJobs = loadFallbackJobs;

        // Generate dynamic filters based on job data
        function generateDynamicFilters() {
            // Generate job type filters
            const jobTypes = {};
            allJobs.forEach(job => {
                jobTypes[job.type] = (jobTypes[job.type] || 0) + 1;
            });

            const jobTypeContainer = document.getElementById('job-type-filters');
            if (jobTypeContainer) {
                jobTypeContainer.innerHTML = '';
                Object.entries(jobTypes).forEach(([type, count]) => {
                    const label = document.createElement('label');
                    label.className = 'flex items-center';
                    label.innerHTML = `
                        <input type="checkbox" name="job-type" value="${type}" class="rounded border-gray-300 text-primary focus:ring-primary">
                        <span class="ml-2 text-gray-700">${type} (${count})</span>
                    `;
                    jobTypeContainer.appendChild(label);
                });
            }

            // Generate salary range filters
            const salaryRanges = {};
            allJobs.forEach(job => {
                salaryRanges[job.salaryRange] = (salaryRanges[job.salaryRange] || 0) + 1;
            });

            const salaryContainer = document.getElementById('salary-filters');
            if (salaryContainer) {
                salaryContainer.innerHTML = '';
                const salaryOrder = ['30k-50k', '50k-75k', '75k-100k', '100k+'];
                salaryOrder.forEach(range => {
                    if (salaryRanges[range]) {
                        const label = document.createElement('label');
                        label.className = 'flex items-center';
                        const displayText = range === '100k+' ? '$100,000+' :
                                          range.replace('k', ',000').replace('-', ' - $').replace(/^/, '$');
                        label.innerHTML = `
                            <input type="checkbox" name="salary" value="${range}" class="rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="ml-2 text-gray-700">${displayText} (${salaryRanges[range]})</span>
                        `;
                        salaryContainer.appendChild(label);
                    }
                });
            }

            // Generate experience level filters
            const experienceLevels = {};
            allJobs.forEach(job => {
                experienceLevels[job.experience] = (experienceLevels[job.experience] || 0) + 1;
            });

            const experienceContainer = document.getElementById('experience-filters');
            if (experienceContainer) {
                experienceContainer.innerHTML = '';
                const expOrder = ['Entry', 'Mid', 'Senior'];
                expOrder.forEach(level => {
                    if (experienceLevels[level]) {
                        const label = document.createElement('label');
                        label.className = 'flex items-center';
                        label.innerHTML = `
                            <input type="checkbox" name="experience" value="${level}" class="rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="ml-2 text-gray-700">${level} Level (${experienceLevels[level]})</span>
                        `;
                        experienceContainer.appendChild(label);
                    }
                });
            }

            // Generate category filters (sync with admin panel values)
            const categories = {
                'software-development': 'Software Development',
                'data-science': 'Data Science',
                'design': 'Design',
                'marketing': 'Marketing',
                'sales': 'Sales',
                'hr': 'Human Resources',
                'finance': 'Finance',
                'operations': 'Operations',
                'other': 'Other'
            };

            const categoryCounts = {};
            allJobs.forEach(job => {
                if (job.category) {
                    const key = String(job.category).toLowerCase();
                    categoryCounts[key] = (categoryCounts[key] || 0) + 1;
                }
            });

            const categoryContainer = document.getElementById('category-filters');
            if (categoryContainer) {
                categoryContainer.innerHTML = '';
                Object.entries(categories).forEach(([value, labelText]) => {
                    const count = categoryCounts[value] || 0;
                    const label = document.createElement('label');
                    label.className = 'flex items-center';
                    label.innerHTML = `
                        <input type="checkbox" name="category" value="${value}" class="rounded border-gray-300 text-primary focus:ring-primary">
                        <span class="ml-2 text-gray-700">${labelText} (${count})</span>
                    `;
                    categoryContainer.appendChild(label);
                });
            }

            // Generate location filters
            const locations = {};
            allJobs.forEach(job => {
                const state = job.location.split(', ')[1]; // Extract state from "City, State"
                if (state) {
                    locations[state] = (locations[state] || 0) + 1;
                }
            });

            const locationContainer = document.getElementById('location-filters');
            if (locationContainer) {
                locationContainer.innerHTML = '';
                Object.entries(locations).forEach(([state, count]) => {
                    const label = document.createElement('label');
                    label.className = 'flex items-center';
                    const stateName = {
                        'CA': 'California',
                        'WA': 'Washington',
                        'TX': 'Texas',
                        'NY': 'New York'
                    }[state] || state;
                    label.innerHTML = `
                        <input type="checkbox" name="location" value="${state}" class="rounded border-gray-300 text-primary focus:ring-primary">
                        <span class="ml-2 text-gray-700">${stateName} (${count})</span>
                    `;
                    locationContainer.appendChild(label);
                });
            }

            // Initialize event listeners for the newly created filter checkboxes
            initializeFilterCheckboxes();
        }

        // Initialize dynamic job system
        function initializeDynamicJobs() {
            // Initialize search functionality for both inputs
            const jobSearchInput = document.getElementById('job-search-input');
            const locationSearchInput = document.getElementById('location-search-input');
            const searchBtn = document.getElementById('search-btn');

            // Add event listeners for real-time search
            if (jobSearchInput) {
                jobSearchInput.addEventListener('input', function() {
                    filterJobs();
                });
            }

            if (locationSearchInput) {
                locationSearchInput.addEventListener('input', function() {
                    filterJobs();
                });
            }

            // Add click event for search button
            if (searchBtn) {
                searchBtn.addEventListener('click', function() {
                    filterJobs();
                });
            }

            // Initialize clear filters button
            const clearFiltersBtn = document.querySelector('.clear-filters-btn');
            if (clearFiltersBtn) {
                clearFiltersBtn.addEventListener('click', function() {
                    clearAllFilters();
                });
            }

            // Note: Filter checkboxes will be initialized after they are generated
        }

        // Initialize filter checkboxes (called after filters are generated)
        function initializeFilterCheckboxes() {
            const filterCheckboxes = document.querySelectorAll('input[type="checkbox"]');
            filterCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    filterJobs();
                });
            });
        }

        // Clear all filters
        function clearAllFilters() {
            // Clear all checkboxes
            const filterCheckboxes = document.querySelectorAll('input[type="checkbox"]');
            filterCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });

            // Clear search input
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = '';
            }

            // Reset filtered jobs to all jobs
            filteredJobs = [...allJobs];
            currentPage = 1;
            
            // Re-render
            renderJobs();
            updateJobCount();
            updatePagination();
            updateFilterCounts();

            // Show notification
            EasyNaukri.showNotification('All filters cleared', 'success');
        }

        // Filter jobs based on search and filters
        function filterJobs() {
            const jobSearchInput = document.getElementById('job-search-input');
            const locationSearchInput = document.getElementById('location-search-input');

            const jobSearchTerm = jobSearchInput ? jobSearchInput.value.toLowerCase().trim() : '';
            const locationSearchTerm = locationSearchInput ? locationSearchInput.value.toLowerCase().trim() : '';

            const selectedFilters = {
                jobType: Array.from(document.querySelectorAll('input[name="job-type"]:checked')).map(cb => cb.value),
                experience: Array.from(document.querySelectorAll('input[name="experience"]:checked')).map(cb => cb.value),
                salary: Array.from(document.querySelectorAll('input[name="salary"]:checked')).map(cb => cb.value),
                location: Array.from(document.querySelectorAll('input[name="location"]:checked')).map(cb => cb.value),
                category: Array.from(document.querySelectorAll('input[name="category"]:checked')).map(cb => cb.value)
            };

            console.log('Filtering jobs with:', { jobSearchTerm, locationSearchTerm, selectedFilters });

            filteredJobs = allJobs.filter(job => {
                // Job title/keywords/company search filter
                const matchesJobSearch = !jobSearchTerm ||
                    job.title.toLowerCase().includes(jobSearchTerm) ||
                    job.company.toLowerCase().includes(jobSearchTerm) ||
                    (job.description && job.description.toLowerCase().includes(jobSearchTerm)) ||
                    (job.skills && Array.isArray(job.skills) && job.skills.some(skill => skill.toLowerCase().includes(jobSearchTerm))) ||
                    (job.category && job.category.toLowerCase().includes(jobSearchTerm));

                // Location search filter
                const matchesLocationSearch = !locationSearchTerm ||
                    job.location.toLowerCase().includes(locationSearchTerm);

                // Job type filter
                const matchesJobType = selectedFilters.jobType.length === 0 ||
                    selectedFilters.jobType.includes(job.type);

                // Experience filter
                const matchesExperience = selectedFilters.experience.length === 0 ||
                    selectedFilters.experience.some(exp => job.experience.includes(exp.replace('+', '')));

                // Salary filter
                const matchesSalary = selectedFilters.salary.length === 0 ||
                    selectedFilters.salary.includes(job.salaryRange);

                // Location filter (from sidebar checkboxes)
                const matchesLocation = selectedFilters.location.length === 0 ||
                    selectedFilters.location.some(loc => job.location.includes(loc));

                // Category filter (from sidebar checkboxes)
                const matchesCategory = selectedFilters.category.length === 0 ||
                    (job.category && selectedFilters.category.includes(String(job.category).toLowerCase()));

                return matchesJobSearch && matchesLocationSearch && matchesJobType && matchesExperience && matchesSalary && matchesLocation && matchesCategory;
            });

            currentPage = 1;
            renderJobs();
            updateJobCount();
            updatePagination();
            updateFilterCounts();
        }

        // Update filter counts based on current filtered jobs
        function updateFilterCounts() {
            // Update job type counts
            const jobTypes = {};
            filteredJobs.forEach(job => {
                jobTypes[job.type] = (jobTypes[job.type] || 0) + 1;
            });

            const jobTypeInputs = document.querySelectorAll('input[name="job-type"]');
            jobTypeInputs.forEach(input => {
                const span = input.nextElementSibling;
                const type = input.value;
                const count = jobTypes[type] || 0;
                span.textContent = `${type} (${count})`;
            });

            // Update salary range counts
            const salaryRanges = {};
            filteredJobs.forEach(job => {
                salaryRanges[job.salaryRange] = (salaryRanges[job.salaryRange] || 0) + 1;
            });

            const salaryInputs = document.querySelectorAll('input[name="salary"]');
            salaryInputs.forEach(input => {
                const span = input.nextElementSibling;
                const range = input.value;
                const count = salaryRanges[range] || 0;
                const displayText = range === '100k+' ? '$100,000+' :
                                  range.replace('k', ',000').replace('-', ' - $').replace(/^/, '$');
                span.textContent = `${displayText} (${count})`;
            });

            // Update experience level counts
            const experienceLevels = {};
            filteredJobs.forEach(job => {
                experienceLevels[job.experience] = (experienceLevels[job.experience] || 0) + 1;
            });

            const experienceInputs = document.querySelectorAll('input[name="experience"]');
            experienceInputs.forEach(input => {
                const span = input.nextElementSibling;
                const level = input.value;
                const count = experienceLevels[level] || 0;
                span.textContent = `${level} Level (${count})`;
            });

            // Update category counts
            const categoryCounts = {};
            filteredJobs.forEach(job => {
                if (job.category) {
                    const key = String(job.category).toLowerCase();
                    categoryCounts[key] = (categoryCounts[key] || 0) + 1;
                }
            });

            const categoryInputs = document.querySelectorAll('input[name="category"]');
            categoryInputs.forEach(input => {
                const span = input.nextElementSibling;
                const value = input.value;
                const labelText = {
                    'software-development': 'Software Development',
                    'data-science': 'Data Science',
                    'design': 'Design',
                    'marketing': 'Marketing',
                    'sales': 'Sales',
                    'hr': 'Human Resources',
                    'finance': 'Finance',
                    'operations': 'Operations',
                    'other': 'Other'
                }[value] || value;
                const count = categoryCounts[value] || 0;
                span.textContent = `${labelText} (${count})`;
            });

            // Update location counts
            const locations = {};
            filteredJobs.forEach(job => {
                const state = job.location.split(', ')[1];
                if (state) {
                    locations[state] = (locations[state] || 0) + 1;
                }
            });

            const locationInputs = document.querySelectorAll('input[name="location"]');
            locationInputs.forEach(input => {
                const span = input.nextElementSibling;
                const state = input.value;
                const count = locations[state] || 0;
                const stateName = {
                    'CA': 'California',
                    'WA': 'Washington',
                    'TX': 'Texas',
                    'NY': 'New York'
                }[state] || state;
                span.textContent = `${stateName} (${count})`;
            });
        }

        // Render jobs dynamically
        function renderJobs() {
            const jobContainer = document.getElementById('jobs-container');
            if (!jobContainer) {
                console.error('Job container not found!');
                return;
            }

            console.log('Rendering jobs...', filteredJobs.length, 'jobs to show');

            // Set container class based on current view
            if (currentView === 'grid') {
                // Better grid layout - more jobs visible
                jobContainer.className = 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4';
            } else {
                jobContainer.className = 'space-y-4 xl:space-y-6 2xl:space-y-8';
            }

            const startIndex = (currentPage - 1) * jobsPerPage;
            const endIndex = startIndex + jobsPerPage;
            const jobsToShow = filteredJobs.slice(startIndex, endIndex);

            jobContainer.innerHTML = '';

            jobsToShow.forEach(job => {
                const jobCard = createJobCard(job);
                jobContainer.appendChild(jobCard);
            });

            // Reinitialize interactive elements
            initializeSkillTags();
            initializeApplyButtons();
        }

        // Create job card HTML
        function createJobCard(job) {
            const jobCard = document.createElement('div');
            const isGridView = currentView === 'grid';

            // Set classes based on view
            if (isGridView) {
                jobCard.className = 'job-card bg-white rounded-lg shadow-md hover:shadow-lg transition p-4 cursor-pointer h-full flex flex-col overflow-hidden';
            } else {
                jobCard.className = 'job-card bg-white rounded-lg shadow-md hover:shadow-lg transition p-6 xl:p-8 2xl:p-10 cursor-pointer overflow-hidden';
            }

            jobCard.setAttribute('data-salary', job.salary);
            jobCard.setAttribute('data-company', job.company);
            jobCard.setAttribute('data-posted', job.posted);
            jobCard.setAttribute('data-job-id', job.id);
            // TEMPORARILY DISABLED - for debugging
            jobCard.onclick = () => {
                console.log('Job card clicked:', job.title, 'ID:', job.id);
                console.log('Full job data:', job);
                EasyNaukri.showNotification(`Job: ${job.title}, Salary: $${job.salary || 0}`, 'info');
                // window.location.href = `job-details.html?id=${job.id}`;
            };

            // Get formatted salary information
            console.log('About to format salary for job:', job.title, 'salary value:', job.salary);
            const salaryInfo = formatSalaryDisplay(job);
            console.log('Formatted salary info:', salaryInfo);

            // Adjust skill tags and description based on view
            const skillTagClass = isGridView ? 'px-2 py-1 text-xs xl:text-sm 2xl:text-base xl:px-3 xl:py-2' : 'px-3 py-1 text-sm xl:text-base 2xl:text-lg xl:px-4 xl:py-2';
            const descriptionClass = isGridView ? 'text-gray-700 mb-3 text-sm xl:text-base 2xl:text-lg' : 'text-gray-700 mb-4 xl:text-lg 2xl:text-xl';
            // Tighter spacing and wrapping on mobile to avoid overflow; keep larger gaps on md+
            const jobInfoClass = isGridView ? 'flex flex-col space-y-1 text-xs xl:text-sm 2xl:text-base text-gray-500' : 'flex flex-wrap items-center gap-1 sm:gap-2 md:gap-4 text-sm xl:text-base 2xl:text-lg text-gray-500';

            const skillsHTML = job.skills.map(skill =>
                `<span class="skill-tag bg-blue-100 text-blue-800 ${skillTagClass} rounded-full cursor-pointer hover:bg-blue-200 transition">${skill}</span>`
            ).join('');

            const postedDate = new Date(job.posted);
            const daysAgo = Math.floor((new Date() - postedDate) / (1000 * 60 * 60 * 24));
            const postedText = daysAgo === 0 ? 'Today' : daysAgo === 1 ? '1 day ago' : `${daysAgo} days ago`;

            // Truncate description for grid view
            const description = isGridView && job.description.length > 60
                ? job.description.substring(0, 60) + '...'
                : job.description;

            // Check if this is a posted job (has postedBy property)
            const isPostedJob = job.postedBy === 'employer';
            const deleteButtonHTML = isPostedJob ? `
                <button class="delete-job-btn p-2 text-gray-400 hover:text-red-500 transition" onclick="event.stopPropagation(); deletePostedJob('${job.id}')" title="Delete Job">
                    <i class="fas fa-trash"></i>
                </button>
            ` : '';

            // Different templates for grid and list views
            if (isGridView) {
                jobCard.innerHTML = `
                    <div class="flex flex-col h-full">
                        <div class="flex items-center justify-between mb-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                                ${job.company.charAt(0)}
                            </div>
                            <div class="flex items-center space-x-1">
                                ${deleteButtonHTML}
                                <button class="bookmark-btn p-1 text-gray-400 hover:text-yellow-500 transition" onclick="event.stopPropagation();">
                                    <i class="far fa-bookmark text-sm"></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 hover:text-primary transition mb-1 line-clamp-2">${job.title}</h3>
                            <p class="text-gray-600 font-medium text-sm mb-2">${job.company}</p>
                            <p class="${descriptionClass} line-clamp-2 flex-1">${description}</p>
                        </div>
                        <div class="mt-3 pt-3 border-t border-gray-100">
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-2 gap-2">
                                <span class="flex-1 min-w-0 truncate"><i class="fas fa-map-marker-alt mr-1"></i>${job.location}</span>
                                <span class="text-green-600 font-bold shrink-0">${salaryInfo.shortAmount || '$0k'}</span>
                            </div>
                            <div class="flex flex-wrap gap-1 mb-2">
                                ${job.skills.slice(0, 2).map(skill =>
                                    `<span class="skill-tag bg-blue-100 text-blue-800 px-2 py-1 text-xs rounded-full">${skill}</span>`
                                ).join('')}
                                ${job.skills.length > 2 ? `<span class="text-xs text-gray-500">+${job.skills.length - 2}</span>` : ''}
                            </div>
                            <div class="text-xs text-gray-500">
                                <span><i class="fas fa-clock mr-1"></i>${postedText}</span>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                jobCard.innerHTML = `
                    <div class="flex items-start justify-between mb-4 flex-wrap gap-2">
                        <div class="flex items-center space-x-4 min-w-0 flex-1">
                            <div class="w-12 h-12 xl:w-16 xl:h-16 2xl:w-20 2xl:h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-lg xl:text-xl 2xl:text-2xl">
                                ${job.company.charAt(0)}
                            </div>
                            <div class="min-w-0">
                                <h3 class="text-xl xl:text-2xl 2xl:text-3xl font-semibold text-gray-900 hover:text-primary transition truncate">${job.title}</h3>
                                <p class="text-gray-600 font-medium xl:text-lg 2xl:text-xl truncate">${job.company}</p>
                                <!-- Mobile-only salary under job title -->
                                <div class="md:hidden text-green-600 font-bold mt-1">
                                    ${salaryInfo.shortAmount || '$0k'}
                                    <span class="text-gray-500 text-sm font-normal">${salaryInfo.period}</span>
                                </div>
                                <div class="${jobInfoClass} mt-2">
                                    <span class="shrink-0"><i class="fas fa-map-marker-alt mr-1"></i>${job.location}</span>
                                    <span><i class="fas fa-clock mr-1"></i>Posted ${postedText}</span>
                                    <span><i class="fas fa-users mr-1"></i>${job.applicants}+ applicants</span>
                                    ${isPostedJob ? '<span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Your Job</span>' : ''}
                                </div>
                            </div>
                        </div>
                        <div class="hidden md:flex items-center space-x-2">
                            <div class="text-right">
                                <div class="text-green-600 font-bold whitespace-nowrap text-base sm:text-lg md:text-xl">${salaryInfo.shortAmount || '$0k'}</div>
                                <div class="text-sm text-gray-500">${salaryInfo.period}</div>
                            </div>
                            ${deleteButtonHTML}
                            <button class="bookmark-btn p-2 text-gray-400 hover:text-yellow-500 transition" onclick="event.stopPropagation();">
                                <i class="far fa-bookmark"></i>
                            </button>
                        </div>
                    </div>
                `;
            }

            // Add common elements for list view only
            if (!isGridView) {
                jobCard.innerHTML += `
                    <p class="${descriptionClass}">${description}</p>

                    <div class="flex flex-wrap gap-2 mb-4">
                        ${skillsHTML}
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                            <span><i class="fas fa-briefcase mr-1"></i>${job.type}</span>
                            <span><i class="fas fa-graduation-cap mr-1"></i>${job.experience}</span>
                        </div>
                        <button class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition font-medium" onclick="event.stopPropagation();">
                            Apply Now
                        </button>
                    </div>
                `;
            }

            return jobCard;
        }

        // Update job count display
        function updateJobCount() {
            const jobCountElement = document.getElementById('job-count');
            if (!jobCountElement) return;

            const totalJobs = filteredJobs.length;
            const startIndex = (currentPage - 1) * jobsPerPage + 1;
            const endIndex = Math.min(currentPage * jobsPerPage, totalJobs);

            if (totalJobs === 0) {
                jobCountElement.textContent = 'No jobs found';
            } else {
                jobCountElement.textContent = `Showing ${startIndex}-${endIndex} of ${totalJobs} jobs`;
            }
        }

        // Update pagination
        function updatePagination() {
            const totalPages = Math.ceil(filteredJobs.length / jobsPerPage);
            const paginationNav = document.getElementById('pagination-nav');

            if (!paginationNav) return;

            // Clear existing pagination
            paginationNav.innerHTML = '';

            if (totalPages <= 1) {
                document.getElementById('pagination-container').style.display = 'none';
                return;
            }

            document.getElementById('pagination-container').style.display = 'flex';

            // Previous button
            const prevButton = document.createElement('button');
            prevButton.id = 'prev-page';
            prevButton.className = 'px-3 py-2 text-gray-500 hover:text-primary transition';
            prevButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
            prevButton.disabled = (currentPage === 1);
            if (prevButton.disabled) {
                prevButton.classList.add('opacity-50', 'cursor-not-allowed');
            }
            prevButton.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    renderJobs();
                    updateJobCount();
                    updatePagination();
                }
            });
            paginationNav.appendChild(prevButton);

            // Page buttons
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            // Adjust start page if we're near the end
            if (endPage - startPage < maxVisiblePages - 1) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            // First page if not visible
            if (startPage > 1) {
                const firstPageBtn = createPageButton(1);
                paginationNav.appendChild(firstPageBtn);

                if (startPage > 2) {
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'px-3 py-2 text-gray-500';
                    ellipsis.textContent = '...';
                    paginationNav.appendChild(ellipsis);
                }
            }

            // Visible page buttons
            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = createPageButton(i);
                paginationNav.appendChild(pageBtn);
            }

            // Last page if not visible
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'px-3 py-2 text-gray-500';
                    ellipsis.textContent = '...';
                    paginationNav.appendChild(ellipsis);
                }

                const lastPageBtn = createPageButton(totalPages);
                paginationNav.appendChild(lastPageBtn);
            }

            // Next button
            const nextButton = document.createElement('button');
            nextButton.id = 'next-page';
            nextButton.className = 'px-3 py-2 text-gray-500 hover:text-primary transition';
            nextButton.innerHTML = '<i class="fas fa-chevron-right"></i>';
            nextButton.disabled = (currentPage === totalPages);
            if (nextButton.disabled) {
                nextButton.classList.add('opacity-50', 'cursor-not-allowed');
            }
            nextButton.addEventListener('click', () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    renderJobs();
                    updateJobCount();
                    updatePagination();
                }
            });
            paginationNav.appendChild(nextButton);
        }

        // Create page button
        function createPageButton(pageNum) {
            const button = document.createElement('button');
            button.className = 'page-btn px-3 py-2 rounded transition';
            button.dataset.page = pageNum;
            button.textContent = pageNum;

            if (pageNum === currentPage) {
                button.classList.add('bg-primary', 'text-white');
            } else {
                button.classList.add('text-gray-700', 'hover:text-primary', 'hover:bg-gray-100');
            }

            button.addEventListener('click', () => {
                currentPage = pageNum;
                renderJobs();
                updateJobCount();
                updatePagination();

                // Scroll to top of job results
                document.getElementById('jobs-container').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            });

            return button;
        }

        // Skill tag filtering
        function initializeSkillTags() {
            const skillTags = document.querySelectorAll('.skill-tag');

            skillTags.forEach(tag => {
                tag.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const skill = this.textContent.trim();

                    // Update search input with skill
                    const searchInput = document.querySelector('.search-input');
                    if (searchInput) {
                        searchInput.value = skill;
                        filterJobs();
                    }

                    EasyNaukri.showNotification(`Searching for jobs with "${skill}" skill...`, 'info');
                });
            });
        }

        // Apply button functionality
        function initializeApplyButtons() {
            // Remove existing event listeners to prevent duplicates
            document.querySelectorAll('button').forEach(button => {
                if (button.textContent.includes('Apply Now')) {
                    // Clone button to remove all event listeners
                    const newButton = button.cloneNode(true);
                    button.parentNode.replaceChild(newButton, button);

                    // Add new event listener
                    newButton.addEventListener('click', async function(e) {
                        e.stopPropagation(); // Prevent job card click

                        // Get job details from the card
                        const jobCard = this.closest('.bg-white');
                        const jobId = jobCard.getAttribute('data-job-id');
                        const jobTitle = jobCard.querySelector('h3').textContent;
                        const company = jobCard.querySelector('p').textContent;

                        // Show loading state
                        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Applying...';
                        this.disabled = true;

                        try {
                            // Find the job in allJobs array
                            const job = allJobs.find(j => j.id === jobId);

                            if (job) {
                                // Priority 1: Check if application URL is available (company career page)
                                if (job.applicationUrl) {
                                    // Redirect to company's application page
                                    EasyNaukri.showNotification(`Redirecting to ${company} career page...`, 'success');
                                    setTimeout(() => {
                                        window.open(job.applicationUrl, '_blank');
                                    }, 500);
                                }
                                // Priority 2: Fallback to email if no URL provided
                                else if (job.applicationEmail) {
                                    const subject = encodeURIComponent(`Application for ${jobTitle}`);
                                    const body = encodeURIComponent(`Dear Hiring Manager,\n\nI am interested in applying for the ${jobTitle} position at ${company}.\n\nPlease find my resume attached.\n\nBest regards,\n[Your Name]`);
                                    EasyNaukri.showNotification('Opening email client...', 'info');
                                    setTimeout(() => {
                                        window.location.href = `mailto:${job.applicationEmail}?subject=${subject}&body=${body}`;
                                    }, 500);
                                }
                                // Priority 3: No application method available
                                else {
                                    EasyNaukri.showNotification('No application method available for this job. Please contact the company directly.', 'warning');
                                }
                            } else {
                                EasyNaukri.showNotification('Job details not found.', 'error');
                            }
                        } catch (error) {
                            console.error('Error processing application:', error);
                            EasyNaukri.showNotification('Error processing application. Please try again.', 'error');
                        }

                        // Reset button state
                        setTimeout(() => {
                            this.innerHTML = 'Apply Now';
                            this.disabled = false;
                        }, 2000);
                    });
                }
            });
        }

        // Show application form modal (DEPRECATED - now redirects to apply-job.html)
        function showApplicationModal(jobTitle, company) {
            // This function is no longer used - Apply Now redirects to apply-job.html
            return;
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto';
            modal.innerHTML = `
                <div class="bg-white rounded-lg max-w-2xl w-full p-6 my-8">
                    <div class="flex justify-between items-center mb-6">
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Apply for Position</h3>
                            <p class="text-gray-600">${jobTitle} at ${company}</p>
                        </div>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600 text-2xl">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form id="applicationForm" class="space-y-6">
                        <input type="hidden" name="jobTitle" value="${jobTitle}">
                        <input type="hidden" name="company" value="${company}">

                        <!-- Personal Information -->
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h4>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                                    <input type="text" name="fullName" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                    <input type="email" name="email" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                                    <input type="tel" name="phone" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                                    <input type="text" name="location" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="City, State">
                                </div>
                            </div>
                        </div>

                        <!-- Experience & Skills -->
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">Experience & Skills</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Years of Experience</label>
                                    <select name="experience" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                        <option value="">Select Experience</option>
                                        <option value="0-1">0-1 years</option>
                                        <option value="2-3">2-3 years</option>
                                        <option value="4-5">4-5 years</option>
                                        <option value="6-10">6-10 years</option>
                                        <option value="10+">10+ years</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Key Skills</label>
                                    <textarea name="skills" rows="3" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="List your relevant skills..."></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Current/Previous Job Title</label>
                                    <input type="text" name="currentJob" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="e.g. Software Developer">
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">Additional Information</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Why are you interested in this position?</label>
                                    <textarea name="interest" rows="3" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Tell us what interests you about this role..."></textarea>
                                </div>
                                <div class="grid md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Expected Salary</label>
                                        <input type="text" name="expectedSalary" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="e.g. $80,000">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Notice Period</label>
                                        <select name="noticePeriod" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                            <option value="">Select Notice Period</option>
                                            <option value="immediate">Immediate</option>
                                            <option value="2weeks">2 weeks</option>
                                            <option value="1month">1 month</option>
                                            <option value="2months">2 months</option>
                                            <option value="3months">3 months</option>
                                        </select>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Resume/CV Link or Summary</label>
                                    <textarea name="resume" rows="4" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Paste your resume link (Google Drive, LinkedIn, etc.) or provide a brief summary of your background..."></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Additional Comments</label>
                                    <textarea name="comments" rows="2" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Any additional information..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end space-x-4 pt-4 border-t">
                            <button type="button" onclick="this.closest('.fixed').remove()" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                                Cancel
                            </button>
                            <button type="submit" class="px-8 py-3 bg-primary text-white rounded-lg hover:bg-secondary transition font-medium">
                                Submit Application
                            </button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // Handle form submission
            const form = modal.querySelector('#applicationForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                handleApplicationSubmission(this);
            });

            // Close modal when clicking outside
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // Handle application form submission
        function handleApplicationSubmission(form) {
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Submitting...';
            submitBtn.disabled = true;

            // Collect form data
            const formData = new FormData(form);
            const applicationData = {};

            for (let [key, value] of formData.entries()) {
                applicationData[key] = value;
            }

            // Add timestamp
            applicationData.submittedAt = new Date().toLocaleString();

            // Send email using EmailJS (you'll need to set this up)
            sendApplicationEmail(applicationData)
                .then(() => {
                    // Show success message
                    EasyNaukri.showNotification('Application submitted successfully! You will receive a confirmation email shortly.', 'success');

                    // Close modal after delay
                    setTimeout(() => {
                        form.closest('.fixed').remove();
                    }, 2000);
                })
                .catch((error) => {
                    console.error('Error sending application:', error);
                    EasyNaukri.showNotification('There was an error submitting your application. Please try again.', 'error');

                    // Reset button
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                });
        }

        // Send application email using EmailJS
        function sendApplicationEmail(applicationData) {
            return new Promise((resolve, reject) => {
                // ADMIN EMAIL CONFIGURATION
                // Replace '<EMAIL>' with your actual admin email address
                const ADMIN_EMAIL = '<EMAIL>';

                // Email sending functionality - ready for EmailJS integration

                console.log('Application Data to be sent to admin:', applicationData);
                console.log('Admin Email:', ADMIN_EMAIL);

                // Simulate API call delay
                setTimeout(() => {
                    // Create email content
                    const emailContent = createEmailContent(applicationData);

                    // In a real implementation, you would use EmailJS like this:
                    /*
                    emailjs.send('YOUR_SERVICE_ID', 'YOUR_TEMPLATE_ID', {
                        to_email: '<EMAIL>',
                        subject: `New Job Application: ${applicationData.jobTitle} at ${applicationData.company}`,
                        message: emailContent,
                        applicant_name: applicationData.fullName,
                        applicant_email: applicationData.email,
                        job_title: applicationData.jobTitle,
                        company: applicationData.company
                    }).then(resolve).catch(reject);
                    */

                    // Application submitted successfully
                    resolve();
                }, 1500);
            });
        }

        // Create formatted email content
        function createEmailContent(data) {
            return `
New Job Application Received

Job Details:
- Position: ${data.jobTitle}
- Company: ${data.company}
- Submitted: ${data.submittedAt}

Applicant Information:
- Name: ${data.fullName}
- Email: ${data.email}
- Phone: ${data.phone}
- Location: ${data.location || 'Not specified'}

Experience & Skills:
- Years of Experience: ${data.experience || 'Not specified'}
- Current/Previous Job: ${data.currentJob || 'Not specified'}
- Key Skills: ${data.skills || 'Not specified'}

Additional Information:
- Interest in Position: ${data.interest || 'Not specified'}
- Expected Salary: ${data.expectedSalary || 'Not specified'}
- Notice Period: ${data.noticePeriod || 'Not specified'}
- Resume/CV: ${data.resume || 'Not provided'}
- Additional Comments: ${data.comments || 'None'}

Please review this application and contact the candidate if suitable.
            `.trim();
        }

        // Sort functionality
        function initializeSortBy() {
            const sortSelect = document.getElementById('sort-select');

            if (!sortSelect) return;

            sortSelect.addEventListener('change', function() {
                const sortValue = this.value;

                // Show sorting notification
                EasyNaukri.showNotification(`Sorting jobs by ${this.options[this.selectedIndex].text.replace('Sort by: ', '')}...`, 'info');

                // Sort the filtered jobs array
                filteredJobs.sort((a, b) => {
                    switch(sortValue) {
                        case 'date':
                            // Sort by date (newest first)
                            const dateA = new Date(a.posted);
                            const dateB = new Date(b.posted);
                            return dateB - dateA;

                        case 'salary-high':
                            // Sort by salary (high to low)
                            return b.salary - a.salary;

                        case 'salary-low':
                            // Sort by salary (low to high)
                            return a.salary - b.salary;

                        case 'company':
                            // Sort by company name
                            return a.company.localeCompare(b.company);

                        case 'relevance':
                        default:
                            // Restore original order by ID
                            return a.id - b.id;
                    }
                });

                // Re-render jobs with new sort order
                currentPage = 1;
                renderJobs();
                updateJobCount();
                updatePagination();
            });
        }

        // Helper function to extract salary number for sorting
        function extractSalary(salaryText) {
            const match = salaryText.match(/\$([0-9,]+)/);
            if (match) {
                return parseInt(match[1].replace(/,/g, ''));
            }
            return 0;
        }

        // View toggle functionality
        function initializeViewToggle() {
            const gridViewBtn = document.getElementById('grid-view-btn');
            const listViewBtn = document.getElementById('list-view-btn');

            if (!gridViewBtn || !listViewBtn) return;

            // List View (default)
            listViewBtn.addEventListener('click', function() {
                // Update button states
                listViewBtn.classList.add('text-primary');
                listViewBtn.classList.remove('text-gray-400');
                gridViewBtn.classList.add('text-gray-400');
                gridViewBtn.classList.remove('text-primary');

                currentView = 'list';
                renderJobs();

                EasyNaukri.showNotification('Switched to List View', 'success');
            });

            // Grid View
            gridViewBtn.addEventListener('click', function() {
                // Update button states
                gridViewBtn.classList.add('text-primary');
                gridViewBtn.classList.remove('text-gray-400');
                listViewBtn.classList.add('text-gray-400');
                listViewBtn.classList.remove('text-primary');

                currentView = 'grid';
                renderJobs();

                EasyNaukri.showNotification('Switched to Grid View', 'success');
            });
        }

        // Pagination functionality
        function initializePagination() {
            const pageButtons = document.querySelectorAll('.page-btn');
            const prevButton = document.getElementById('prev-page');
            const nextButton = document.getElementById('next-page');

            // Page button clicks
            pageButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const page = parseInt(this.dataset.page);
                    goToPage(page);
                });
            });

            // Previous button
            prevButton.addEventListener('click', function() {
                const totalPages = Math.ceil(filteredJobs.length / jobsPerPage);
                if (currentPage > 1) {
                    goToPage(currentPage - 1);
                }
            });

            // Next button
            nextButton.addEventListener('click', function() {
                const totalPages = Math.ceil(filteredJobs.length / jobsPerPage);
                if (currentPage < totalPages) {
                    goToPage(currentPage + 1);
                }
            });

            function goToPage(page) {
                currentPage = page;

                // Show notification
                EasyNaukri.showNotification(`Loading page ${page}...`, 'info');

                // Re-render jobs for new page
                renderJobs();
                updateJobCount();
                updatePagination();

                // Scroll to top of job listings
                setTimeout(() => {
                    document.getElementById('jobs-container').scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    EasyNaukri.showNotification(`Page ${page} loaded successfully`, 'success');
                }, 300);
            }
        }



        // Handle window resize for responsive layout
        function handleResize() {
            // Re-render jobs to apply correct grid classes for current screen size
            if (typeof renderJobs === 'function') {
                renderJobs();
            }
        }

        // Add resize listener
        window.addEventListener('resize', debounce(handleResize, 250));

        // Debounce function to limit resize event frequency
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>
